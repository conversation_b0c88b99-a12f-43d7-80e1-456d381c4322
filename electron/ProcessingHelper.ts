// ProcessingHelper.ts

import { app } from 'electron';
import fs from "fs/promises"; // Added fs/promises
import path from "path"; // Added path
import { AppState } from "./main"
import { LLMHelper } from "./LLMHelper"
// import dotenv from "dotenv" // No longer needed if hardcoding

// const MOCK_API_WAIT_TIME = Number(process.env.MOCK_API_WAIT_TIME) || 500; // This would also need a hardcoded value or default if used

// Define the path for the history file, similar to ipcHandlers
const historyFilePath = path.join(app.getPath('userData'), 'analysis_history.json');

// Helper function to read the history file (can be refactored later)
async function readHistoryFile(): Promise<any[]> {
  try {
    await fs.access(historyFilePath);
    const data = await fs.readFile(historyFilePath, 'utf-8');
    return JSON.parse(data);
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      return [];
    } else if (error instanceof SyntaxError) {
      console.error("[ProcessingHelper] Error parsing history file, returning empty history:", error);
      return [];
    }
    console.error("[ProcessingHelper] Error reading history file:", error);
    return [];
  }
}

// Helper function to write to the history file (can be refactored later)
async function writeHistoryFile(history: any[]): Promise<void> {
  try {
    await fs.writeFile(historyFilePath, JSON.stringify(history, null, 2), 'utf-8');
  } catch (error) {
    console.error("[ProcessingHelper] Error writing history file:", error);
    throw error;
  }
}

export class ProcessingHelper {
  private appState: AppState
  private llmHelper: LLMHelper
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null

  constructor(appState: AppState) {
    this.appState = appState

    // --- Hardcoded API Key ---
    // IMPORTANT: Replace "YOUR_ACTUAL_GEMINI_API_KEY_HERE" with your real API key
    const apiKey = "AIzaSyAT6HRh0qun-VBz5z5wfy50qfO0siDihKw";
    // --- End of Hardcoded API Key ---

    if (!apiKey) {
      const errorMsg = "GEMINI_API_KEY is not set or empty (hardcoded).";
      console.error(`[API_KEY_CHECK] ${errorMsg}`);
      throw new Error(errorMsg);
    }
    console.log('[API_KEY_CHECK] Using hardcoded GEMINI_API_KEY.');
    this.llmHelper = new LLMHelper(apiKey)
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.appState.getMainWindow()
    if (!mainWindow) return

    const view = this.appState.getView()

    if (view === "queue") {
      const screenshotQueue = this.appState.getScreenshotHelper().getScreenshotQueue()
      if (screenshotQueue.length === 0) {
        mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.NO_SCREENSHOTS)
        return
      }

      // Check if last screenshot is an audio file
      const allPaths = this.appState.getScreenshotHelper().getScreenshotQueue();
      const lastPath = allPaths[allPaths.length - 1];
      if (lastPath.endsWith('.mp3') || lastPath.endsWith('.wav')) {
        mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.INITIAL_START);
        this.appState.setView('solutions');
        try {
          const audioResult = await this.llmHelper.analyzeAudioFile(lastPath);
          mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.PROBLEM_EXTRACTED, audioResult);
          this.appState.setProblemInfo({ problem_statement: audioResult.text, input_format: {}, output_format: {}, constraints: [], test_cases: [] });
          return;
        } catch (err: any) {
          console.error('Audio processing error:', err);
          mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR, err.message);
          return;
        }
      }

      // NEW: Handle screenshot as plain text (like audio)
      mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.INITIAL_START)
      this.appState.setView("solutions")
      this.currentProcessingAbortController = new AbortController()
      try {
        const imageResult = await this.llmHelper.analyzeImageFile(lastPath);
        
        // --- Save to history --- START ---
        try {
          const history = await readHistoryFile();
          history.push(imageResult); // imageResult is the structured object
          await writeHistoryFile(history);
          console.log("[ProcessingHelper] Analysis record saved to history.");

          const currentHistory = await readHistoryFile();
          console.log("[ProcessingHelper] Current analysis history:", JSON.stringify(currentHistory, null, 2));
        } catch (saveError: any) {
          console.error("[ProcessingHelper] Failed to save analysis record to history:", saveError);
        }
        // --- Save to history --- END ---

        const problemInfo = {
          recordId: imageResult.id,
          problem_statement: {
            description: imageResult.description,
            entities: imageResult.entities,
            rawResponse: imageResult.rawResponse // or imageResult.text, they are the same
          },
          input_format: { description: "Generated from screenshot", parameters: [] as any[] },
          output_format: { description: "Generated from screenshot", type: "string", subtype: "image_analysis_details" }, // Changed subtype
          complexity: { time: "N/A", space: "N/A" },
          test_cases: [] as any[],
          validation_type: "manual",
          difficulty: "custom"
        };
        mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.PROBLEM_EXTRACTED, problemInfo);
        this.appState.setProblemInfo(problemInfo);
      } catch (error: any) {
        console.error("Image processing error:", error)
        mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR, error.message)
      } finally {
        this.currentProcessingAbortController = null
      }
      return;
    } else {
      // Debug mode
      const extraScreenshotQueue = this.appState.getScreenshotHelper().getExtraScreenshotQueue()
      if (extraScreenshotQueue.length === 0) {
        console.log("No extra screenshots to process")
        mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.NO_SCREENSHOTS)
        return
      }

      mainWindow.webContents.send(this.appState.PROCESSING_EVENTS.DEBUG_START)
      this.currentExtraProcessingAbortController = new AbortController()

      try {
        // Get problem info and current solution
        const problemInfo = this.appState.getProblemInfo()
        if (!problemInfo) {
          throw new Error("No problem info available")
        }

        // Get current solution from state
        const currentSolution = await this.llmHelper.generateSolution(problemInfo)
        const currentCode = currentSolution.solution.code

        // Debug the solution using vision model
        const debugResult = await this.llmHelper.debugSolutionWithImages(
          problemInfo,
          currentCode,
          extraScreenshotQueue
        )

        this.appState.setHasDebugged(true)
        mainWindow.webContents.send(
          this.appState.PROCESSING_EVENTS.DEBUG_SUCCESS,
          debugResult
        )

      } catch (error: any) {
        console.error("Debug processing error:", error)
        mainWindow.webContents.send(
          this.appState.PROCESSING_EVENTS.DEBUG_ERROR,
          error.message
        )
      } finally {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  public cancelOngoingRequests(): void {
    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
    }

    this.appState.setHasDebugged(false)
  }

  public async processAudioBase64(data: string, mimeType: string) {
    // Directly use LLMHelper to analyze inline base64 audio
    return this.llmHelper.analyzeAudioFromBase64(data, mimeType);
  }

  // Add audio file processing method
  public async processAudioFile(filePath: string) {
    return this.llmHelper.analyzeAudioFile(filePath);
  }

  public getLLMHelper() {
    return this.llmHelper;
  }
}
