import { BrowserWindow, screen } from "electron"
import { AppState } from "main"
import path from "node:path"

const isDev = process.env.NODE_ENV === "development"

const startUrl = isDev
  ? "http://localhost:5180"
  : `file://${path.join(__dirname, "../dist/index.html")}`

export class WindowHelper {
  private mainWindow: BrowserWindow | null = null
  private isWindowVisible: boolean = false
  private windowPosition: { x: number; y: number } | null = null
  private windowSize: { width: number; height: number } | null = null
  private appState: AppState

  // Initialize with explicit number type and 0 value
  private screenWidth: number = 0
  private screenHeight: number = 0
  private step: number = 0
  private currentX: number = 0
  private currentY: number = 0

  constructor(appState: AppState) {
    this.appState = appState
  }

  public setWindowDimensions(width: number, height: number): void {
    if (!this.mainWindow || this.mainWindow.isDestroyed()) return

    // Get current window position
    const [currentX, currentY] = this.mainWindow.getPosition()

    // Get screen dimensions
    const primaryDisplay = screen.getPrimaryDisplay()
    const workArea = primaryDisplay.workAreaSize

    // Use 75% width if debugging has occurred, otherwise use 60%
    const maxAllowedWidth = Math.floor(
      workArea.width * (this.appState.getHasDebugged() ? 0.75 : 0.5)
    )

    // Ensure width doesn't exceed max allowed width and height is reasonable
    const newWidth = Math.min(width + 32, maxAllowedWidth)
    const newHeight = Math.ceil(height)

    // Center the window horizontally if it would go off screen
    const maxX = workArea.width - newWidth
    const newX = Math.min(Math.max(currentX, 0), maxX)

    // Update window bounds
    this.mainWindow.setBounds({
      x: newX,
      y: currentY,
      width: newWidth,
      height: newHeight
    })

    // Update internal state
    this.windowPosition = { x: newX, y: currentY }
    this.windowSize = { width: newWidth, height: newHeight }
    this.currentX = newX
  }

  public createWindow(): void {
    if (this.mainWindow !== null) return

    const primaryDisplay = screen.getPrimaryDisplay()
    const workArea = primaryDisplay.workAreaSize
    this.screenWidth = workArea.width
    this.screenHeight = workArea.height

    // Define initial dimensions for centering
    const initialWindowWidth = 600; // Adjusted initial width
    const initialWindowHeight = 150; // Adjusted initial height, can be small before content loads

    // Calculate centered position
    // Ensure initial dimensions are not larger than work area
    const actualInitialWidth = Math.min(initialWindowWidth, workArea.width);
    const actualInitialHeight = Math.min(initialWindowHeight, workArea.height);

    const centeredX = primaryDisplay.workArea.x + Math.max(0, Math.floor((workArea.width - actualInitialWidth) / 2));
    // Position at the very top of the work area
    const topY = primaryDisplay.workArea.y; // Set to the top of the work area

    this.step = Math.floor(this.screenWidth / 10) // For window movement, keep as is
    // this.currentX = 0; // REMOVED: No longer starting at the left

    const windowSettings: Electron.BrowserWindowConstructorOptions = {
      width: actualInitialWidth,
      height: actualInitialHeight,
      x: centeredX,
      y: topY,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: true,
        preload: path.join(__dirname, "preload.js")
      },
      show: true,
      alwaysOnTop: true,
      frame: false,
      transparent: true,
      fullscreenable: false,
      hasShadow: false,
      backgroundColor: "#00000000",
      focusable: true
    }

    this.mainWindow = new BrowserWindow(windowSettings)
    // this.mainWindow.webContents.openDevTools() // Commented out to prevent auto-opening DevTools
    this.mainWindow.setContentProtection(true)

    if (process.platform === "darwin") {
      this.mainWindow.setVisibleOnAllWorkspaces(true, {
        visibleOnFullScreen: true
      })
      this.mainWindow.setHiddenInMissionControl(true)
      this.mainWindow.setAlwaysOnTop(true, "floating")
    }
    if (process.platform === "linux") {
      // Linux-specific optimizations for stealth overlays
      if (this.mainWindow.setHasShadow) {
        this.mainWindow.setHasShadow(false)
      }
      this.mainWindow.setFocusable(false)
    } 
    this.mainWindow.setSkipTaskbar(true)
    this.mainWindow.setAlwaysOnTop(true)

    // Clear cache before loading URL to ensure fresh content
    this.mainWindow.webContents.session.clearCache()
      .then(() => {
        console.log("[WindowHelper] Cache cleared for session.");
        // Ensure mainWindow is not null before calling loadURL
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          return this.mainWindow.loadURL(startUrl);
        }
        return Promise.reject(new Error("MainWindow became unavailable before loading URL after cache clear."));
      })
      .catch((err) => {
        console.error("Failed to load URL or clear cache:", err);
      });

    const bounds = this.mainWindow.getBounds()
    this.windowPosition = { x: bounds.x, y: bounds.y }
    this.windowSize = { width: bounds.width, height: bounds.height }
    this.currentX = bounds.x
    this.currentY = bounds.y

    this.setupWindowListeners()
    this.isWindowVisible = true
  }

  private setupWindowListeners(): void {
    if (!this.mainWindow) return

    this.mainWindow.on("move", () => {
      if (this.mainWindow) {
        const bounds = this.mainWindow.getBounds()
        this.windowPosition = { x: bounds.x, y: bounds.y }
        this.currentX = bounds.x
        this.currentY = bounds.y
      }
    })

    this.mainWindow.on("resize", () => {
      if (this.mainWindow) {
        const bounds = this.mainWindow.getBounds()
        this.windowSize = { width: bounds.width, height: bounds.height }
      }
    })

    this.mainWindow.on("closed", () => {
      this.mainWindow = null
      this.isWindowVisible = false
      this.windowPosition = null
      this.windowSize = null
    })
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  public isVisible(): boolean {
    return this.isWindowVisible
  }

  public hideMainWindow(): void {
    console.log('[WindowHelper.hideMainWindow] Attempting to hide window.');
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      console.warn('[WindowHelper.hideMainWindow] Main window does not exist or is destroyed. Cannot hide.');
      return;
    }

    try {
      const bounds = this.mainWindow.getBounds();
      this.windowPosition = { x: bounds.x, y: bounds.y };
      this.windowSize = { width: bounds.width, height: bounds.height };
      console.log(`[WindowHelper.hideMainWindow] Stored position: ${JSON.stringify(this.windowPosition)}, size: ${JSON.stringify(this.windowSize)}`);
      this.mainWindow.hide();
      this.isWindowVisible = false;
      console.log('[WindowHelper.hideMainWindow] Window hidden. isWindowVisible set to false.');
    } catch (error) {
      console.error('[WindowHelper.hideMainWindow] Error during hide:', error);
    }
  }

  public showMainWindow(): void {
    console.log('[WindowHelper.showMainWindow] Attempting to show window.');
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      console.warn('[WindowHelper.showMainWindow] Main window does not exist or is destroyed. Cannot show.');
      return;
    }

    console.log(`[WindowHelper.showMainWindow] Current state before show: isWindowVisible=${this.isWindowVisible}, storedPosition=${JSON.stringify(this.windowPosition)}, storedSize=${JSON.stringify(this.windowSize)}`);

    if (this.windowPosition && this.windowSize) {
      try {
        console.log(`[WindowHelper.showMainWindow] Attempting to setBounds to: x=${this.windowPosition.x}, y=${this.windowPosition.y}, width=${this.windowSize.width}, height=${this.windowSize.height}`);
      this.mainWindow.setBounds({
        x: this.windowPosition.x,
        y: this.windowPosition.y,
        width: this.windowSize.width,
        height: this.windowSize.height
        });
        console.log('[WindowHelper.showMainWindow] setBounds successful.');
      } catch (error) {
        console.error('[WindowHelper.showMainWindow] Error during setBounds:', error);
      }
    } else {
      console.warn('[WindowHelper.showMainWindow] windowPosition or windowSize is null. Cannot set bounds accurately. Window will show at default/previous position.');
    }

    try {
      console.log('[WindowHelper.showMainWindow] Calling mainWindow.showInactive().');
      this.mainWindow.showInactive();
      console.log('[WindowHelper.showMainWindow] mainWindow.showInactive() called.');
      if (this.mainWindow && !this.mainWindow.isDestroyed()) { // Guard against race condition if window closed
        console.log('[WindowHelper.showMainWindow] Attempting to focus main window.');
        this.mainWindow.focus();
        console.log('[WindowHelper.showMainWindow] mainWindow.focus() called.');
      }
      this.isWindowVisible = true;
      console.log('[WindowHelper.showMainWindow] Window shown. isWindowVisible set to true.');
    } catch (error) {
      console.error('[WindowHelper.showMainWindow] Error during showInactive:', error);
    }
  }

  public toggleMainWindow(): void {
    if (this.isWindowVisible) {
      this.hideMainWindow()
    } else {
      this.showMainWindow()
    }
  }

  // New methods for window movement
  public moveWindowRight(): void {
    if (!this.mainWindow) return

    const windowWidth = this.windowSize?.width || 0
    const halfWidth = windowWidth / 2

    // Ensure currentX and currentY are numbers
    this.currentX = Number(this.currentX) || 0
    this.currentY = Number(this.currentY) || 0

    this.currentX = Math.min(
      this.screenWidth - halfWidth,
      this.currentX + this.step
    )
    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    )
  }

  public moveWindowLeft(): void {
    if (!this.mainWindow) return

    const windowWidth = this.windowSize?.width || 0
    const halfWidth = windowWidth / 2

    // Ensure currentX and currentY are numbers
    this.currentX = Number(this.currentX) || 0
    this.currentY = Number(this.currentY) || 0

    this.currentX = Math.max(-halfWidth, this.currentX - this.step)
    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    )
  }

  public moveWindowDown(): void {
    if (!this.mainWindow) return

    const windowHeight = this.windowSize?.height || 0
    const halfHeight = windowHeight / 2

    // Ensure currentX and currentY are numbers
    this.currentX = Number(this.currentX) || 0
    this.currentY = Number(this.currentY) || 0

    this.currentY = Math.min(
      this.screenHeight - halfHeight,
      this.currentY + this.step
    )
    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    )
  }

  public moveWindowUp(): void {
    if (!this.mainWindow) return

    const windowHeight = this.windowSize?.height || 0
    const halfHeight = windowHeight / 2

    // Ensure currentX and currentY are numbers
    this.currentX = Number(this.currentX) || 0
    this.currentY = Number(this.currentY) || 0

    this.currentY = Math.max(-halfHeight, this.currentY - this.step)
    this.mainWindow.setPosition(
      Math.round(this.currentX),
      Math.round(this.currentY)
    )
  }
}
