import { GoogleGenerativeAI, GenerativeModel, GenerateContentRequest, Part } from "@google/generative-ai"
import fs from "fs"
import crypto from "crypto"

// Define a type for the structured analysis result (conceptual for JS)
// interface ScreenshotAnalysisResult {
//   id: string;
//   timestamp: number;
//   imagePath: string;
//   description: string;
//   entities: Array<{ type: string; value: string }>;
//   rawResponse: string;
// }

export class LLMHelper {
  private model: GenerativeModel
  private readonly systemPrompt = `You are <PERSON><PERSON>, a helpful, proactive assistant for any kind of problem or situation (not just coding). For any user input, analyze the situation, provide a clear problem statement, relevant context, and suggest several possible responses or actions the user could take next. Always explain your reasoning. Present your suggestions as a list of options or next steps.`

  constructor(apiKey: string) {
    const genAI = new GoogleGenerativeAI(apiKey)
    this.model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" })
  }

  private async fileToGenerativePart(imagePath: string) {
    const imageData = await fs.promises.readFile(imagePath)
    return {
      inlineData: {
        data: imageData.toString("base64"),
        mimeType: "image/png"
      }
    }
  }

  private cleanJsonResponse(text: string): string {
    // Remove markdown code block syntax if present
    text = text.replace(/^```(?:json)?\n/, '').replace(/\n```$/, '');
    // Remove any leading/trailing whitespace
    text = text.trim();
    return text;
  }

  private _parseImageAnalysisResponse(rawText: string): { description: string; entities: Array<{ type: string; value: string }> } {
    const lines = rawText.trim().split('\n');
    let description = "";
    const entities: Array<{ type: string; value: string }> = [];
    let readingDescription = false;
    let readingEntities = false;

    if (lines.length > 0 && lines[0].trim() === "Screenshot Result") {
      lines.shift(); // Remove "Screenshot Result" line
      readingDescription = true;
    }

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith("Identified entities:")) {
        readingDescription = false;
        readingEntities = true;
        continue; // Skip this line itself
      }
      if (trimmedLine === "No specific entities identified.") {
        readingDescription = false;
        readingEntities = false;
        break; // Stop processing
      }

      if (readingDescription) {
        // Append to description, ensuring not to capture entity-related lines prematurely
        // This assumes description is usually 1-2 sentences before entities.
        if (!trimmedLine.startsWith("- ")) { // Heuristic to avoid mixing entity lines if format slightly varies
            description += (description ? "\n" : "") + trimmedLine;
        } else { // If we hit something that looks like an entity, stop description
            readingDescription = false;
            readingEntities = true; 
            // and process this line as an entity if it fits the pattern
        }
      }
      
      // Re-check readingEntities in case it was set by the description block
      if (readingEntities && trimmedLine.startsWith("- ")) {
        const entityLine = trimmedLine.substring(2).trim(); // Remove "- "
        const separatorIndex = entityLine.indexOf(":");
        if (separatorIndex > 0) {
          const type = entityLine.substring(0, separatorIndex).trim();
          const value = entityLine.substring(separatorIndex + 1).trim();
          if (type && value) {
            entities.push({ type, value });
          }
        }
      }
    }
    // If description ended up empty because it was immediately followed by entities.
     if (!description && lines.length > 0 && !lines[0].trim().startsWith("Identified entities:") && !lines[0].trim().startsWith("No specific entities identified.") && !lines[0].trim().startsWith("- ")) {
        description = lines[0].trim();
    }


    return { description: description.trim(), entities };
  }

  public async extractProblemFromImages(imagePaths: string[]) {
    try {
      const imageParts = await Promise.all(imagePaths.map(path => this.fileToGenerativePart(path)))
      
      const prompt = `${this.systemPrompt}\n\nYou are a wingman. Please analyze these images and extract the following information in JSON format:\n{
  "problem_statement": "A clear statement of the problem or situation depicted in the images.",
  "context": "Relevant background or context from the images.",
  "suggested_responses": ["First possible answer or action", "Second possible answer or action", "..."],
  "reasoning": "Explanation of why these suggestions are appropriate."
}\nImportant: Return ONLY the JSON object, without any markdown formatting or code blocks.`

      const result = await this.model.generateContent([prompt, ...imageParts])
      const response = await result.response
      const text = this.cleanJsonResponse(response.text())
      return JSON.parse(text)
    } catch (error) {
      console.error("Error extracting problem from images:", error)
      throw error
    }
  }

  public async generateSolution(problemInfo: any) {
    const prompt = `${this.systemPrompt}\n\nGiven this problem or situation:\n${JSON.stringify(problemInfo, null, 2)}\n\nPlease provide your response in the following JSON format:\n{
  "solution": {
    "code": "The code or main answer here.",
    "problem_statement": "Restate the problem or situation.",
    "context": "Relevant background/context.",
    "suggested_responses": ["First possible answer or action", "Second possible answer or action", "..."],
    "reasoning": "Explanation of why these suggestions are appropriate."
  }
}\nImportant: Return ONLY the JSON object, without any markdown formatting or code blocks.`

    console.log("[LLMHelper] Calling Gemini LLM for solution...");
    try {
      const result = await this.model.generateContent(prompt)
      console.log("[LLMHelper] Gemini LLM returned result.");
      const response = await result.response
      const text = this.cleanJsonResponse(response.text())
      const parsed = JSON.parse(text)
      console.log("[LLMHelper] Parsed LLM response:", parsed)
      return parsed
    } catch (error) {
      console.error("[LLMHelper] Error in generateSolution:", error);
      throw error;
    }
  }

  public async debugSolutionWithImages(problemInfo: any, currentCode: string, debugImagePaths: string[]) {
    try {
      const imageParts = await Promise.all(debugImagePaths.map(path => this.fileToGenerativePart(path)))
      
      const prompt = `${this.systemPrompt}\n\nYou are a wingman. Given:\n1. The original problem or situation: ${JSON.stringify(problemInfo, null, 2)}\n2. The current response or approach: ${currentCode}\n3. The debug information in the provided images\n\nPlease analyze the debug information and provide feedback in this JSON format:\n{
  "solution": {
    "code": "The code or main answer here.",
    "problem_statement": "Restate the problem or situation.",
    "context": "Relevant background/context.",
    "suggested_responses": ["First possible answer or action", "Second possible answer or action", "..."],
    "reasoning": "Explanation of why these suggestions are appropriate."
  }
}\nImportant: Return ONLY the JSON object, without any markdown formatting or code blocks.`

      const result = await this.model.generateContent([prompt, ...imageParts])
      const response = await result.response
      const text = this.cleanJsonResponse(response.text())
      const parsed = JSON.parse(text)
      console.log("[LLMHelper] Parsed debug LLM response:", parsed)
      return parsed
    } catch (error) {
      console.error("Error debugging solution with images:", error)
      throw error
    }
  }

  public async analyzeAudioFile(audioPath: string) {
    try {
      const audioData = await fs.promises.readFile(audioPath);
      const audioPart = {
        inlineData: {
          data: audioData.toString("base64"),
          mimeType: "audio/mp3"
        }
      };
      const prompt = `${this.systemPrompt}\n\nAnalyze this audio clip. Provide your response in markdown format.
Your response should include:
1. A "**Description:**" section with a concise summary of the audio.
2. A "**Potential Actions/Responses:**" section with a bulleted or numbered list of suggestions.

Example:
**Description:**
The audio contains sounds of a cat purring and birds chirping.

**Potential Actions/Responses:**
- Check if the cat is nearby.
- Open a window to hear the birds more clearly.
- Consider if this is a recording or live sounds.

Do not return a structured JSON object. Only markdown text.`;
      const result = await this.model.generateContent([prompt, audioPart]);
      const response = await result.response;
      const text = response.text();
      return { text, timestamp: Date.now() };
    } catch (error) {
      console.error("Error analyzing audio file:", error);
      throw error;
    }
  }

  public async analyzeAudioFromBase64(data: string, mimeType: string) {
    try {
      const audioPart = {
        inlineData: {
          data,
          mimeType
        }
      };
      const prompt = `${this.systemPrompt}\n\nAnalyze this audio clip. Provide your response in markdown format.
Your response should include:
1. A "**Description:**" section with a concise summary of the audio.
2. A "**Potential Actions/Responses:**" section with a bulleted or numbered list of suggestions.

Example:
**Description:**
The audio contains sounds of a cat purring and birds chirping.

**Potential Actions/Responses:**
- Check if the cat is nearby.
- Open a window to hear the birds more clearly.
- Consider if this is a recording or live sounds.

Do not return a structured JSON object. Only markdown text.`;
      const result = await this.model.generateContent([prompt, audioPart]);
      const response = await result.response;
      const text = response.text();
      return { text, timestamp: Date.now() };
    } catch (error) {
      console.error("Error analyzing audio from base64:", error);
      throw error;
    }
  }

  public async analyzeImageFile(imagePath: string) {
    try {
      const imageData = await fs.promises.readFile(imagePath);
      const imagePart = {
        inlineData: {
          data: imageData.toString("base64"),
          mimeType: "image/png"
        }
      };
      const prompt = `DO NOT SUGGEST ACTIONS OR NEXT STEPS. YOUR ONLY TASK IS TO DESCRIBE THE IMAGE AND EXTRACT ENTITIES.

TASK: Analyze the provided screenshot.
OUTPUT REQUIREMENTS:
1. Start your response *exactly* with "Screenshot Result".
2. Provide a very brief (1-2 sentences) description of the image content.
3. After the description, list any identifiable entities. Use the following format for entities:
    - Person: [Name]
    - Email: [Email Address]
    - Business: [Business Name]
    - Product/Item: [Product or Item Name]
    - Key Info: [Other important text or data]
4. If no specific entities of these types are clearly identifiable, state "No specific entities identified." after the description.
5. DO NOT include any other text, explanations, or suggestions.

Example of a valid response:
Screenshot Result
This image shows a social media profile page.
Identified entities:
- Person: Saminu Salisu
- Business: LinkedIn
- Product/Item: LinkedIn News
- Key Info: "Data Engineering | PhD Candidate"

Example of a valid response if no entities are found:
Screenshot Result
This image shows a photograph of a landscape.
No specific entities identified.

FAILURE TO ADHERE TO THIS FORMAT AND TASK WILL RESULT IN AN INVALID RESPONSE.
ONLY PROVIDE THE DESCRIPTION AND THE ENTITY LIST AS SPECIFIED.`;
      const result = await this.model.generateContent([prompt, imagePart]);
      const response = await result.response;
      const rawText = response.text();
      
      const { description, entities } = this._parseImageAnalysisResponse(rawText);
      const id = crypto.randomUUID();
      const timestamp = Date.now();

      return {
        id,
        timestamp,
        imagePath,
        description,
        entities,
        rawResponse: rawText,
        // We still return the raw text for direct display in the UI as per current functionality
        // The structured data is for storage and potential future, more structured UI display
        text: rawText 
      };
    } catch (error) {
      console.error("Error analyzing image file:", error);
      throw error;
    }
  }

  public async askFollowUpOnScreenshotResult(previousAnalysis: string, followUpQuestion: string): Promise<{ text: string; timestamp: number }> {
    try {
      // Focused prompt for direct answers, relying on LLM's general knowledge if context is insufficient.
      // No direct search grounding tool is available in SDK v0.2.1.
      const focusedPrompt = `You are a helpful AI assistant.
Context from previous analysis:
---
${previousAnalysis}
---
User's follow-up question: "${followUpQuestion}"

Please provide a concise and direct answer to the user's follow-up question. If the context from the previous analysis is not sufficient, use your general knowledge to answer. Focus solely on answering the question directly and avoid verbose explanations or structured formats like problem statements or next steps unless the question explicitly asks for them.`;

      // console.log("[LLMHelper] Asking follow-up. Prompt:", focusedPrompt);

      // SDK v0.2.1 does not support the 'tools' parameter for search grounding.
      // The generateContent method in this version expects a string or an array of Parts.
      const result = await this.model.generateContent(focusedPrompt);
      const response = await result.response;
      const text = response.text(); // response.text() is synchronous in this SDK version for simple text generation.
      
      // console.log("[LLMHelper] Follow-up response:", text);
      return { text, timestamp: Date.now() };
    } catch (error) {
      console.error("Error asking follow-up on screenshot result:", error);
      throw error;
    }
  }
} 