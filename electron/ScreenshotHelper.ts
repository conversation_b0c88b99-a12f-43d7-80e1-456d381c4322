// ScreenshotHelper.ts

import path from "node:path"
import fs from "node:fs"
import { v4 as uuidv4 } from "uuid"
import screenshotDesktop from "screenshot-desktop"

export class ScreenshotHelper {
  private screenshotDir: string
  private screenshotQueue: string[] = []
  private extraScreenshotQueue: string[] = []

  private view: "queue" | "solutions"

  constructor(view: "queue" | "solutions", screenshotDir: string) {
    this.view = view
    this.screenshotDir = screenshotDir

    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true })
    }
  }

  public getView(): "queue" | "solutions" {
    return this.view
  }

  public setView(view: "queue" | "solutions"): void {
    this.view = view
  }

  public getScreenshotQueue(): string[] {
    return this.screenshotQueue
  }

  public getExtraScreenshotQueue(): string[] {
    return this.extraScreenshotQueue
  }

  public clearQueues(): void {
    this.screenshotQueue = []
    this.extraScreenshotQueue = []
    console.log("[ScreenshotHelper.clearQueues] Queues cleared.")
  }

  public async takeScreenshot(
    onBeforeCapture: () => void,
    onAfterCapture: () => void
  ): Promise<string> {
    const filename = `${uuidv4()}.png`
    const tempPath = path.join(this.screenshotDir, filename)
    let capturedPath: string

    console.log(`[ScreenshotHelper.takeScreenshot] Initiated for view: ${this.view}`)

    if (this.view === "queue") {
      if (onBeforeCapture) {
        console.log('[ScreenshotHelper.takeScreenshot] QueueView: Calling onBeforeCapture (hideMainWindow).')
        onBeforeCapture()
      }
      
      console.log('[ScreenshotHelper.takeScreenshot] QueueView: Starting 1000ms delay before screenshot.')
      await new Promise(resolve => setTimeout(resolve, 1000))
      console.log('[ScreenshotHelper.takeScreenshot] QueueView: Delay finished. Taking screenshot.')
      
      try {
        capturedPath = await screenshotDesktop({ filename: tempPath, format: 'png' })
        console.log(`[ScreenshotHelper.takeScreenshot] QueueView: Screenshot taken: ${capturedPath}`)
          } catch (error) {
        console.error('[ScreenshotHelper.takeScreenshot] QueueView: Error during screenshotDesktop:', error)
        if (onAfterCapture) {
          console.log('[ScreenshotHelper.takeScreenshot] QueueView: Screenshot failed, calling onAfterCapture (showMainWindow) due to error.')
          onAfterCapture()
        }
        throw error
      }

      if (onAfterCapture) {
        console.log('[ScreenshotHelper.takeScreenshot] QueueView: Screenshot successful, calling onAfterCapture (showMainWindow).')
        onAfterCapture()
          }
      this.screenshotQueue.push(capturedPath)
      console.log(`[ScreenshotHelper.takeScreenshot] Added to screenshotQueue. Queue size: ${this.screenshotQueue.length}`)
    } else {
      console.log(`[ScreenshotHelper.takeScreenshot] NonQueueView (${this.view}): Taking screenshot directly.`)
      try {
        capturedPath = await screenshotDesktop({ filename: tempPath, format: 'png' })
        console.log(`[ScreenshotHelper.takeScreenshot] NonQueueView (${this.view}): Screenshot taken: ${capturedPath}`)
          } catch (error) {
        console.error(`[ScreenshotHelper.takeScreenshot] NonQueueView (${this.view}): Error during screenshotDesktop:`, error)
        throw error
      }
      this.extraScreenshotQueue.push(capturedPath)
      console.log(`[ScreenshotHelper.takeScreenshot] Added to extraScreenshotQueue. Queue size: ${this.extraScreenshotQueue.length}`)
    }
    
    return capturedPath
  }

  public async getImagePreview(filePath: string): Promise<string> {
    if (!fs.existsSync(filePath)) {
      console.error(`[ScreenshotHelper.getImagePreview] File not found: ${filePath}`)
      throw new Error(`File not found: ${filePath}`)
    }
    try {
      const fileBuffer = await fs.promises.readFile(filePath)
      return `data:image/png;base64,${fileBuffer.toString('base64')}`
    } catch (error) {
      console.error(`[ScreenshotHelper.getImagePreview] Error reading file ${filePath}:`, error)
      throw error
    }
  }

  public async deleteScreenshot(filePath: string): Promise<{ success: boolean; error?: string }> {
    console.log(`[ScreenshotHelper.deleteScreenshot] Attempting to delete: ${filePath}`)
    try {
      if (fs.existsSync(filePath)) {
        await fs.promises.unlink(filePath)
        console.log(`[ScreenshotHelper.deleteScreenshot] Successfully deleted: ${filePath}`)
        this.screenshotQueue = this.screenshotQueue.filter(p => p !== filePath)
        this.extraScreenshotQueue = this.extraScreenshotQueue.filter(p => p !== filePath)
        return { success: true }
      } else {
        console.warn(`[ScreenshotHelper.deleteScreenshot] File not found, cannot delete: ${filePath}`)
        return { success: false, error: "File not found" }
      }
    } catch (error: any) {
      console.error(`[ScreenshotHelper.deleteScreenshot] Error deleting file ${filePath}:`, error)
      return { success: false, error: error.message }
    }
  }
}
