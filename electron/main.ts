// import 'dotenv/config'; // Load environment variables from .env file - REMOVED
import { app, <PERSON>rowserWindow } from "electron"
import fs from 'fs'
import path from 'path'
import util from 'util'
import { initializeIpcHandlers } from "./ipcHandlers"
import { WindowHelper } from "./WindowHelper"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { ShortcutsHelper } from "./shortcuts"
import { ProcessingHelper } from "./ProcessingHelper"

// --- Start of logging setup ---
// Ensure app is ready before calling getPath, or provide a fallback for early errors
let logDir: string
try {
  logDir = app.getPath('userData')
} catch (e) {
  // Fallback if app.getPath is not available yet (e.g., error before app is ready)
  const homeDir = process.env.HOME || process.env.USERPROFILE
  logDir = homeDir ? path.join(homeDir, '.interview-coder-logs') : path.join('.', '.interview-coder-logs')
  console.warn(`[Logging] app.getPath('userData') failed, falling back to: ${logDir}`, e)
}

if (!fs.existsSync(logDir)) {
  try {
    fs.mkdirSync(logDir, { recursive: true })
  } catch (mkdirError) {
    process.stderr.write(`[Logging] Failed to create log directory: ${logDir} - ${mkdirError}\n`)
    // If we can't create the log directory, logging to file won't work.
    // We'll still log to stderr.
  }
}

const logFile = path.join(logDir, 'main-process.log')

try {
  if (fs.existsSync(logFile)) {
    fs.unlinkSync(logFile) // Delete old log file
  }
} catch (unlinkError) {
  process.stderr.write(`[Logging] Failed to delete old log file: ${logFile} - ${unlinkError}\n`)
}

const logStream = fs.createWriteStream(logFile, { flags: 'a' })

const originalConsoleLog = console.log
const originalConsoleError = console.error

console.log = (...args) => {
  const timestamp = new Date().toISOString()
  const message = util.format(...args)
  const logMessage = `[LOG] ${timestamp}: ${message}\n`
  if (logStream && logStream.writable) {
    logStream.write(logMessage)
  }
  originalConsoleLog.apply(console, [`[LOG] ${timestamp}:`, ...args])
}

console.error = (...args) => {
  const timestamp = new Date().toISOString()
  const message = util.format(...args)
  let logMessage = `[ERROR] ${timestamp}: ${message}\n`
  if (args[0] instanceof Error && args[0].stack) {
    logMessage += `[ERROR_STACK] ${timestamp}: ${args[0].stack}\n`
  }
  if (logStream && logStream.writable) {
    logStream.write(logMessage)
  }
  originalConsoleError.apply(console, [`[ERROR] ${timestamp}:`, ...args])
}

process.on('uncaughtException', (error) => {
  console.error('Uncaught Main Process Exception:', error)
  // Consider whether to quit the app on critical errors
  // setTimeout(() => app.quit(), 1000) // Graceful shutdown
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Main Process Rejection at:', promise, 'reason:', reason)
})
// --- End of logging setup ---

export class AppState {
  private static instance: AppState | null = null

  private windowHelper: WindowHelper
  private screenshotHelper: ScreenshotHelper
  public shortcutsHelper: ShortcutsHelper
  public processingHelper: ProcessingHelper

  // View management
  private view: "queue" | "solutions" = "queue"

  private problemInfo: {
    problem_statement: string
    input_format: Record<string, any>
    output_format: Record<string, any>
    constraints: Array<Record<string, any>>
    test_cases: Array<Record<string, any>>
  } | null = null // Allow null

  private hasDebugged: boolean = false

  // Processing events
  public readonly PROCESSING_EVENTS = {
    //global states
    UNAUTHORIZED: "procesing-unauthorized",
    NO_SCREENSHOTS: "processing-no-screenshots",

    //states for generating the initial solution
    INITIAL_START: "initial-start",
    PROBLEM_EXTRACTED: "problem-extracted",
    SOLUTION_SUCCESS: "solution-success",
    INITIAL_SOLUTION_ERROR: "solution-error",

    //states for processing the debugging
    DEBUG_START: "debug-start",
    DEBUG_SUCCESS: "debug-success",
    DEBUG_ERROR: "debug-error"
  } as const

  constructor() {
    // Initialize WindowHelper with this
    this.windowHelper = new WindowHelper(this)

    // Corrected ScreenshotHelper initialization
    const screenshotsPath = path.join(app.getPath("userData"), "screenshots")
    this.screenshotHelper = new ScreenshotHelper(this.view, screenshotsPath)
    console.log(`[AppState.constructor] Initialized ScreenshotHelper with path: ${screenshotsPath}`)

    // Initialize ProcessingHelper
    this.processingHelper = new ProcessingHelper(this)

    // Initialize ShortcutsHelper
    this.shortcutsHelper = new ShortcutsHelper(this)
  }

  public static getInstance(): AppState {
    if (!AppState.instance) {
      AppState.instance = new AppState()
    }
    return AppState.instance
  }

  // Getters and Setters
  public getMainWindow(): BrowserWindow | null {
    return this.windowHelper.getMainWindow()
  }

  public getView(): "queue" | "solutions" {
    return this.view
  }

  public setView(view: "queue" | "solutions"): void {
    this.view = view
    this.screenshotHelper.setView(view)
  }

  public isVisible(): boolean {
    return this.windowHelper.isVisible()
  }

  public getScreenshotHelper(): ScreenshotHelper {
    return this.screenshotHelper
  }

  public getProblemInfo(): any {
    return this.problemInfo
  }

  public setProblemInfo(problemInfo: any): void {
    this.problemInfo = problemInfo
  }

  public getScreenshotQueue(): string[] {
    return this.screenshotHelper.getScreenshotQueue()
  }

  public getExtraScreenshotQueue(): string[] {
    return this.screenshotHelper.getExtraScreenshotQueue()
  }

  // Window management methods
  public createWindow(): void {
    this.windowHelper.createWindow()
  }

  public hideMainWindow(): void {
    this.windowHelper.hideMainWindow()
  }

  public showMainWindow(): void {
    this.windowHelper.showMainWindow()
  }

  public toggleMainWindow(): void {
    console.log(
      "Screenshots: ",
      this.screenshotHelper.getScreenshotQueue().length,
      "Extra screenshots: ",
      this.screenshotHelper.getExtraScreenshotQueue().length
    )
    this.windowHelper.toggleMainWindow()
  }

  public setWindowDimensions(width: number, height: number): void {
    this.windowHelper.setWindowDimensions(width, height)
  }

  public clearQueues(): void {
    this.screenshotHelper.clearQueues()

    // Clear problem info
    this.problemInfo = null

    // Reset view to initial state
    this.setView("queue")
  }

  // Screenshot management methods
  public async takeScreenshot(): Promise<string> {
    if (!this.getMainWindow()) throw new Error("No main window available")

    const screenshotPath = await this.screenshotHelper.takeScreenshot(
      () => this.hideMainWindow(),
      () => this.showMainWindow()
    )

    return screenshotPath
  }

  public async getImagePreview(filepath: string): Promise<string> {
    return this.screenshotHelper.getImagePreview(filepath)
  }

  public async deleteScreenshot(
    path: string
  ): Promise<{ success: boolean; error?: string }> {
    return this.screenshotHelper.deleteScreenshot(path)
  }

  // New methods to move the window
  public moveWindowLeft(): void {
    this.windowHelper.moveWindowLeft()
  }

  public moveWindowRight(): void {
    this.windowHelper.moveWindowRight()
  }
  public moveWindowDown(): void {
    this.windowHelper.moveWindowDown()
  }
  public moveWindowUp(): void {
    this.windowHelper.moveWindowUp()
  }

  public setHasDebugged(value: boolean): void {
    this.hasDebugged = value
  }

  public getHasDebugged(): boolean {
    return this.hasDebugged
  }
}

// Application initialization
async function initializeApp() {
  const appState = AppState.getInstance()

  // Initialize IPC handlers before window creation
  initializeIpcHandlers(appState, appState.processingHelper.getLLMHelper())

  app.whenReady().then(() => {
    console.log("App is ready")
    appState.createWindow()
    // Register global shortcuts using ShortcutsHelper
    appState.shortcutsHelper.registerGlobalShortcuts()
  })

  app.on("activate", () => {
    console.log("App activated")
    if (appState.getMainWindow() === null) {
      appState.createWindow()
    }
  })

  // Quit when all windows are closed, except on macOS
  app.on("window-all-closed", () => {
    if (process.platform !== "darwin") {
      app.quit()
    }
  })

  app.dock?.hide() // Hide dock icon (optional)
  app.commandLine.appendSwitch("disable-background-timer-throttling")
}

// Start the application
initializeApp().catch(console.error)
