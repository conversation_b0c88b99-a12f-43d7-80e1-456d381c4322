import { context<PERSON><PERSON>, ip<PERSON><PERSON><PERSON><PERSON>, <PERSON>pc<PERSON>enderer<PERSON>vent } from "electron"

// Define the structure for a single analysis record (for clarity in preload)
interface ScreenshotAnalysisRecord {
  id: string;
  timestamp: number;
  imagePath: string;
  description: string;
  entities: Array<{ type: string; value: string }>;
  rawResponse: string;
  text: string; // Still passing raw text for current UI
}

// Types for the exposed Electron API
interface ElectronAPI {
  updateContentDimensions: (dimensions: {
    width: number
    height: number
  }) => Promise<void>
  getScreenshots: () => Promise<Array<{ path: string; preview: string }>>
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => () => void
  onSolutionsReady: (callback: (solutions: string) => void) => () => void
  onResetView: (callback: () => void) => () => void
  onSolutionStart: (callback: () => void) => () => void
  onDebugStart: (callback: () => void) => () => void
  onDebugSuccess: (callback: (data: any) => void) => () => void
  onSolutionError: (callback: (error: string) => void) => () => void
  onProcessingNoScreenshots: (callback: () => void) => () => void
  onProblemExtracted: (callback: (data: any) => void) => () => void
  onSolutionSuccess: (callback: (data: any) => void) => () => void

  onUnauthorized: (callback: () => void) => () => void
  onDebugError: (callback: (error: string) => void) => () => void
  takeScreenshot: () => Promise<{ path: string; preview: string } | null>
  moveWindowLeft: () => Promise<void>
  moveWindowRight: () => Promise<void>
  analyzeAudioFromBase64: (data: string, mimeType: string) => Promise<any>
  analyzeAudioFile: (path: string) => Promise<any>
  analyzeImageFile: (imagePath: string) => Promise<ScreenshotAnalysisRecord>
  askFollowUpOnScreenshotResult: (previousAnalysis: string, followUpQuestion: string) => Promise<{ text: string; timestamp: number }>
  quitApp: () => void
  saveAnalysisRecord: (record: ScreenshotAnalysisRecord) => Promise<{ success: boolean; error?: string }>
  getAnalysisHistory: () => Promise<{ success: boolean; error?: string; history: ScreenshotAnalysisRecord[] }>
  deleteAnalysisRecord: (recordId: string) => Promise<{ success: boolean; error?: string }>
  clearAnalysisHistory: () => Promise<{ success: boolean; error?: string }>
  onToggleWindowLock: (callback: () => void) => () => void
  searchPersonLocal: (args: { personName: string; recordId: string }) => Promise<{ success: boolean; data?: string; error?: string; historyError?: string }>
}

export const PROCESSING_EVENTS = {
  //global states
  UNAUTHORIZED: "procesing-unauthorized",
  NO_SCREENSHOTS: "processing-no-screenshots",

  //states for generating the initial solution
  INITIAL_START: "initial-start",
  PROBLEM_EXTRACTED: "problem-extracted",
  SOLUTION_SUCCESS: "solution-success",
  INITIAL_SOLUTION_ERROR: "solution-error",

  //states for processing the debugging
  DEBUG_START: "debug-start",
  DEBUG_SUCCESS: "debug-success",
  DEBUG_ERROR: "debug-error"
} as const

// Expose the Electron API to the renderer process
contextBridge.exposeInMainWorld("electronAPI", {
  updateContentDimensions: (dimensions: { width: number; height: number }) =>
    ipcRenderer.invoke("update-content-dimensions", dimensions),
  takeScreenshot: () => ipcRenderer.invoke("take-screenshot"),
  getScreenshots: () => ipcRenderer.invoke("get-screenshots"),
  deleteScreenshot: (path: string) =>
    ipcRenderer.invoke("delete-screenshot", path),

  // Event listeners
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => {
    const subscription = (_: any, data: { path: string; preview: string }) =>
      callback(data)
    ipcRenderer.on("screenshot-taken", subscription)
    return () => {
      ipcRenderer.removeListener("screenshot-taken", subscription)
    }
  },
  onSolutionsReady: (callback: (solutions: string) => void) => {
    const subscription = (_: any, solutions: string) => callback(solutions)
    ipcRenderer.on("solutions-ready", subscription)
    return () => {
      ipcRenderer.removeListener("solutions-ready", subscription)
    }
  },
  onResetView: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("reset-view", subscription)
    return () => {
      ipcRenderer.removeListener("reset-view", subscription)
    }
  },
  onSolutionStart: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.INITIAL_START, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.INITIAL_START, subscription)
    }
  },
  onDebugStart: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.DEBUG_START, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.DEBUG_START, subscription)
    }
  },

  onDebugSuccess: (callback: (data: any) => void) => {
    ipcRenderer.on("debug-success", (_event, data) => callback(data))
    return () => {
      ipcRenderer.removeListener("debug-success", (_event, data) =>
        callback(data)
      )
    }
  },
  onDebugError: (callback: (error: string) => void) => {
    const subscription = (_: any, error: string) => callback(error)
    ipcRenderer.on(PROCESSING_EVENTS.DEBUG_ERROR, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.DEBUG_ERROR, subscription)
    }
  },
  onSolutionError: (callback: (error: string) => void) => {
    const subscription = (_: any, error: string) => callback(error)
    ipcRenderer.on(PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
        subscription
      )
    }
  },
  onProcessingNoScreenshots: (
    callback: () => void
  ): (() => void) => {
    const handler = () => callback()
    ipcRenderer.on("processing-no-screenshots", handler)
    return () => {
      ipcRenderer.removeListener("processing-no-screenshots", handler)
    }
  },

  // Listener for when the initial processing/extraction starts
  onInitialStart: (callback: () => void): (() => void) => {
    const handler = () => callback();
    ipcRenderer.on("initial-start", handler);
    return () => {
      ipcRenderer.removeListener("initial-start", handler);
    };
  },

  // Listener for when the problem statement has been extracted
  onProblemExtracted: (callback: (data: any) => void): (() => void) => {
    const handler = (_event: IpcRendererEvent, data: any) => callback(data);
    ipcRenderer.on("problem-extracted", handler);
    return () => {
      ipcRenderer.removeListener("problem-extracted", handler);
    };
  },
  onSolutionSuccess: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on(PROCESSING_EVENTS.SOLUTION_SUCCESS, subscription)
    return () => {
      ipcRenderer.removeListener(
        PROCESSING_EVENTS.SOLUTION_SUCCESS,
        subscription
      )
    }
  },
  onUnauthorized: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(PROCESSING_EVENTS.UNAUTHORIZED, subscription)
    return () => {
      ipcRenderer.removeListener(PROCESSING_EVENTS.UNAUTHORIZED, subscription)
    }
  },
  moveWindowLeft: () => ipcRenderer.invoke("move-window-left"),
  moveWindowRight: () => ipcRenderer.invoke("move-window-right"),
  analyzeAudioFromBase64: (data: string, mimeType: string) => ipcRenderer.invoke("analyze-audio-base64", data, mimeType),
  analyzeAudioFile: (path: string) => ipcRenderer.invoke("analyze-audio-file", path),
  analyzeImageFile: (path: string) => ipcRenderer.invoke("analyze-image-file", path),
  askFollowUpOnScreenshotResult: (previousAnalysis: string, followUpQuestion: string) => ipcRenderer.invoke("ask-follow-up-on-screenshot-result", previousAnalysis, followUpQuestion),
  quitApp: () => ipcRenderer.invoke("quit-app"),
  saveAnalysisRecord: (record: ScreenshotAnalysisRecord) => ipcRenderer.invoke("save-analysis-record", record),
  getAnalysisHistory: () => ipcRenderer.invoke("get-analysis-history"),
  deleteAnalysisRecord: (recordId: string) => ipcRenderer.invoke("delete-analysis-record", recordId),
  clearAnalysisHistory: () => ipcRenderer.invoke("clear-analysis-history"),
  onToggleWindowLock: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on("toggle-window-lock", subscription)
    return () => {
      ipcRenderer.removeListener("toggle-window-lock", subscription)
    }
  },
  searchPersonLocal: (args: { personName: string; recordId: string }) => ipcRenderer.invoke("search-person-local", args)
} as ElectronAPI)
