// ipcHandlers.ts

import { ipcMain, app, BrowserView } from "electron"
import { AppState } from "./main"
import fs from "fs/promises"; // Use promises version of fs
import path from "path";
import { LLMHelper } from "./LLMHelper";
// import * as ExaModule from "exa-js"; // Previous attempt
// const Exa = (ExaModule as any).default || ExaModule; // Previous attempt
// const Exa = require("exa-js"); // REMOVED
import crypto from "crypto";

// Define the structure for a single analysis record (for clarity)
// interface ScreenshotAnalysisRecord {
//   id: string;
//   timestamp: number;
//   imagePath: string;
//   description: string;
//   entities: Array<{ type: string; value: string }>;
//   rawResponse: string;
// }

const historyFilePath = path.join(app.getPath('userData'), 'analysis_history.json');

async function readHistoryFile(): Promise<any[]> {
  try {
    await fs.access(historyFilePath); // Check if file exists
    const data = await fs.readFile(historyFilePath, 'utf-8');
    return JSON.parse(data);
  } catch (error: any) {
    if (error.code === 'ENOENT') { // File does not exist
      return [];
    } else if (error instanceof SyntaxError) { // JSON parsing error
      console.error("Error parsing history file, returning empty history:", error);
      return []; // Or handle by trying to recover/backup the corrupted file
    }
    console.error("Error reading history file:", error);
    return []; // Return empty array on other errors as well
  }
}

async function writeHistoryFile(history: any[]): Promise<void> {
  try {
    await fs.writeFile(historyFilePath, JSON.stringify(history, null, 2), 'utf-8');
  } catch (error) {
    console.error("Error writing history file:", error);
    throw error; // Re-throw to be caught by the handler
  }
}

export function initializeIpcHandlers(appState: AppState, llmHelper: LLMHelper): void {
  ipcMain.handle(
    "update-content-dimensions",
    async (event, { width, height }: { width: number; height: number }) => {
      if (width && height) {
        appState.setWindowDimensions(width, height)
      }
    }
  )

  ipcMain.handle("delete-screenshot", async (event, path: string) => {
    return appState.deleteScreenshot(path)
  })

  ipcMain.handle("take-screenshot", async () => {
    try {
      const screenshotPath = await appState.takeScreenshot()
      const preview = await appState.getImagePreview(screenshotPath)
      return { path: screenshotPath, preview }
    } catch (error) {
      console.error("Error taking screenshot:", error)
      throw error
    }
  })

  ipcMain.handle("get-screenshots", async () => {
    console.log({ view: appState.getView() })
    try {
      let previews = []
      if (appState.getView() === "queue") {
        previews = await Promise.all(
          appState.getScreenshotQueue().map(async (path) => ({
            path,
            preview: await appState.getImagePreview(path)
          }))
        )
      } else {
        previews = await Promise.all(
          appState.getExtraScreenshotQueue().map(async (path) => ({
            path,
            preview: await appState.getImagePreview(path)
          }))
        )
      }
      previews.forEach((preview: any) => console.log(preview.path))
      return previews
    } catch (error) {
      console.error("Error getting screenshots:", error)
      throw error
    }
  })

  ipcMain.handle("toggle-window", async () => {
    appState.toggleMainWindow()
  })

  ipcMain.handle("reset-queues", async () => {
    try {
      appState.clearQueues()
      console.log("Screenshot queues have been cleared.")
      return { success: true }
    } catch (error: any) {
      console.error("Error resetting queues:", error)
      return { success: false, error: error.message }
    }
  })

  // IPC handler for analyzing audio from base64 data
  ipcMain.handle("analyze-audio-base64", async (event, data: string, mimeType: string) => {
    try {
      const result = await appState.processingHelper.processAudioBase64(data, mimeType)
      return result
    } catch (error: any) {
      console.error("Error in analyze-audio-base64 handler:", error)
      throw error
    }
  })

  // IPC handler for analyzing audio from file path
  ipcMain.handle("analyze-audio-file", async (event, path: string) => {
    try {
      const result = await appState.processingHelper.processAudioFile(path)
      return result
    } catch (error: any) {
      console.error("Error in analyze-audio-file handler:", error)
      throw error
    }
  })

  // IPC handler for analyzing image from file path
  ipcMain.handle("analyze-image-file", async (event, imagePath: string) => {
    try {
      const result = await llmHelper.analyzeImageFile(imagePath);
      return { success: true, ...result };
    } catch (error: any) {
      console.error("Error analyzing image file via IPC:", error);
      return { success: false, error: error.message };
    }
  })

  ipcMain.handle("ask-follow-up-on-screenshot-result", async (event, previousAnalysis: string, followUpQuestion: string) => {
    const activeLlmHelper = appState.processingHelper?.getLLMHelper() || llmHelper;
    if (!activeLlmHelper) {
        console.error("LLMHelper not available in ask-follow-up IPC handler");
        return { success: false, error: "LLM service not configured for follow-up questions." };
    }
    try {
      const result = await activeLlmHelper.askFollowUpOnScreenshotResult(previousAnalysis, followUpQuestion);
      return { success: true, ...result };
    } catch (error: any) {
      console.error("Error asking follow-up question via IPC:", error);
      return { success: false, error: error.message };
    }
  })

  ipcMain.handle("quit-app", () => {
    app.quit()
  })

  // Handler to save a full analysis record
  ipcMain.handle("save-analysis-record", async (event, record: any) => {
    try {
      const history = await readHistoryFile();
      history.push(record);
      await writeHistoryFile(history);
      return { success: true };
    } catch (error: any) {
      console.error("Failed to save analysis record:", error);
      return { success: false, error: error.message };
    }
  });

  // Handler to get the entire analysis history
  ipcMain.handle("get-analysis-history", async () => {
    try {
      const history = await readHistoryFile();
      return { success: true, history };
    } catch (error: any) {
      console.error("Failed to get analysis history:", error);
      return { success: false, error: error.message, history: [] };
    }
  });

  // Handler to delete a specific analysis record
  ipcMain.handle("delete-analysis-record", async (event, recordId: string) => {
    try {
      const history = await readHistoryFile();
      const filteredHistory = history.filter(record => record.id !== recordId);
      await writeHistoryFile(filteredHistory);
      return { success: true };
    } catch (error: any) {
      console.error("Failed to delete analysis record:", error);
      return { success: false, error: error.message };
    }
  });

  // Handler to clear all analysis history
  ipcMain.handle("clear-analysis-history", async () => {
    try {
      await writeHistoryFile([]);
      return { success: true };
    } catch (error: any) {
      console.error("Failed to clear analysis history:", error);
      return { success: false, error: error.message };
    }
  });

  ipcMain.handle("search-person-local", async (event, { personName, recordId }: { personName: string; recordId: string }) => {
    console.log(`[IPC] search-person-local called for: ${personName} (Record ID: ${recordId})`);
    if (!recordId) {
      console.error("[IPC] search-person-local: recordId is required to save search to history.");
      return { success: false, error: "recordId is missing, cannot save to history." };
    }

    const requestBody = {
      messages: [
        {
          role: "user",
          content: `Search for ${personName}`,
        },
      ],
      thread_id: "__default__",
      enable_background_investigation: true,
      auto_accepted_plan: true,
    };

    try {
      const response = await fetch("http://localhost:8000/api/chat/stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `[IPC] Local service request failed with status ${response.status}: ${errorText}`
        );
        return {
          success: false,
          error: `Service request failed: ${response.status} - ${errorText}`,
        };
      }

      // Attempt to read the streamed response as text
      // This might need adjustment based on how the stream is formatted (e.g., Server-Sent Events)
      const responseData = await response.text(); 
      console.log("[IPC] Local service response data:", responseData);

      // Save to history
      try {
        const history = await readHistoryFile(); // Assumes ScreenshotAnalysisRecord[]
        const recordIndex = history.findIndex(r => r.id === recordId);

        if (recordIndex === -1) {
          console.error(`[IPC] search-person-local: Record with ID ${recordId} not found in history.`);
          // Still return success for the search itself, but log history error
          return { success: true, data: responseData, historyError: "Record not found to save search." };
        }

        if (!history[recordIndex].personSearches) {
          history[recordIndex].personSearches = [];
        }
        history[recordIndex].personSearches.push({
          personName,
          serviceResponse: responseData,
          timestamp: Date.now(),
        });

        await writeHistoryFile(history);
        console.log(`[IPC] Person search for '${personName}' saved to history for record ${recordId}.`);
      } catch (historyError: any) {
        console.error("[IPC] search-person-local: Error saving person search to history:", historyError);
        // Do not let history saving error fail the main operation
        return { success: true, data: responseData, historyError: historyError.message };
      }

      return { success: true, data: responseData };
    } catch (error: any) {
      console.error("[IPC] Error calling local service:", error);
      return {
        success: false,
        error: error.message || "An unexpected error occurred with the local service.",
      };
    }
  });
}
