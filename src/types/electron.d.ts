export interface ScreenshotAnalysisRecord {
  id: string;
  timestamp: number;
  imagePath: string;
  description: string;
  entities: Array<{ type: string; value: string }>;
  rawResponse: string;
  text?: string; // Optional as it might not be strictly needed by all parts using this type directly from history
}

export interface ElectronAPI {
  updateContentDimensions: (dimensions: {
    width: number
    height: number
  }) => Promise<void>
  getScreenshots: () => Promise<Array<{ path: string; preview: string }>>
  deleteScreenshot: (path: string) => Promise<{ success: boolean; error?: string }>
  onScreenshotTaken: (callback: (data: { path: string; preview: string }) => void) => () => void
  onSolutionsReady: (callback: (solutions: string) => void) => () => void
  onResetView: (callback: () => void) => () => void
  onSolutionStart: (callback: () => void) => () => void
  onDebugStart: (callback: () => void) => () => void
  onDebugSuccess: (callback: (data: any) => void) => () => void
  onSolutionError: (callback: (error: string) => void) => () => void
  onProcessingNoScreenshots: (callback: () => void) => () => void
  onProblemExtracted: (callback: (data: any) => void) => () => void
  onSolutionSuccess: (callback: (data: any) => void) => () => void
  onUnauthorized: (callback: () => void) => () => void
  onDebugError: (callback: (error: string) => void) => () => void
  takeScreenshot: () => Promise<void>
  moveWindowLeft: () => Promise<void>
  moveWindowRight: () => Promise<void>
  analyzeAudioFromBase64: (data: string, mimeType: string) => Promise<{ text: string; timestamp: number }>
  analyzeAudioFile: (path: string) => Promise<{ text: string; timestamp: number }>
  analyzeImageFile: (path: string) => Promise<ScreenshotAnalysisRecord>
  askFollowUpOnScreenshotResult: (previousAnalysis: string, followUpQuestion: string) => Promise<{ text: string; timestamp: number }>
  quitApp: () => Promise<void>
  onInitialStart: (callback: () => void) => () => void
  saveAnalysisRecord: (record: ScreenshotAnalysisRecord) => Promise<{ success: boolean; error?: string }>
  getAnalysisHistory: () => Promise<{ success: boolean; error?: string; history: ScreenshotAnalysisRecord[] }>
  deleteAnalysisRecord: (recordId: string) => Promise<{ success: boolean; error?: string }>
  clearAnalysisHistory: () => Promise<{ success: boolean; error?: string }>
  searchEntityWithExa: (entityValue: string) => Promise<{ success: boolean; data?: any[]; error?: string; details?: string }>
  searchPersonLocal: (args: { personName: string; recordId: string }) => Promise<{ success: boolean; data?: string; error?: string; historyError?: string }>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
} 