import { ToastProvider } from "./components/ui/toast"
import Queue from "./_pages/Queue"
import { ToastViewport } from "@radix-ui/react-toast"
import { useEffect, useRef, useState } from "react"
import Solutions from "./_pages/Solutions"
import { QueryClient, QueryClientProvider } from "react-query"
import AnalysisHistory from "./components/History/AnalysisHistory"
import QueueCommands from './components/Queue/QueueCommands'

// Removed the conflicting inline 'declare global' block
// The global ElectronAPI type is now solely defined in src/types/electron.d.ts

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: Infinity,
      cacheTime: Infinity
    }
  }
})

const App: React.FC = () => {
  const [view, setView] = useState<"queue" | "solutions" | "debug" | "history">("queue")
  const containerRef = useRef<HTMLDivElement>(null)
  const [isQueueCommandsTooltipVisible, setIsQueueCommandsTooltipVisible] = useState(false)
  const [queueCommandsTooltipHeight, setQueueCommandsTooltipHeight] = useState(0)

  const handleQueueCommandsTooltipVisibility = (visible: boolean, height: number) => {
    setIsQueueCommandsTooltipVisible(visible)
    setQueueCommandsTooltipHeight(height)
    // This function might be needed if the tooltip affects overall layout height handled by App.tsx
    // For now, primarily for prop satisfaction.
  }

  // Effect for height monitoring
  useEffect(() => {
    const cleanup = window.electronAPI.onResetView(() => {
      console.log("Received 'reset-view' message from main process.")
      queryClient.invalidateQueries(["screenshots"])
      queryClient.invalidateQueries(["problem_statement"])
      queryClient.invalidateQueries(["solution"])
      queryClient.invalidateQueries(["new_solution"])
      setView("queue")
    })

    return () => {
      cleanup()
    }
  }, [])

  useEffect(() => {
    if (!containerRef.current) return

    const updateHeight = () => {
      if (!containerRef.current) return
      let height = containerRef.current.scrollHeight
      // If showing QueueCommands tooltip and it's outside the normal flow / affects overall height:
      // if (isQueueCommandsTooltipVisible) height += queueCommandsTooltipHeight; 
      // This logic depends on how tooltip is positioned and if it pushes other content.
      // For now, let's assume QueueCommands manages its own tooltip within its bounds primarily.

      const width = containerRef.current.scrollWidth
      window.electronAPI?.updateContentDimensions({ width, height })
    }

    const resizeObserver = new ResizeObserver(() => {
      updateHeight()
    })

    // Initial height update
    updateHeight()

    // Observe for changes
    resizeObserver.observe(containerRef.current)

    // Also update height when view changes
    const mutationObserver = new MutationObserver(() => {
      updateHeight()
    })

    mutationObserver.observe(containerRef.current, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true
    })

    return () => {
      resizeObserver.disconnect()
      mutationObserver.disconnect()
    }
  }, [view, isQueueCommandsTooltipVisible, queueCommandsTooltipHeight]) // Re-run when view or tooltip state changes

  useEffect(() => {
    const cleanupFunctions = [
      window.electronAPI.onSolutionStart(() => {
        setView("solutions")
        console.log("starting processing")
      }),

      window.electronAPI.onUnauthorized(() => {
        queryClient.removeQueries(["screenshots"])
        queryClient.removeQueries(["solution"])
        queryClient.removeQueries(["problem_statement"])
        setView("queue")
        console.log("Unauthorized")
      }),
      // Update this reset handler
      window.electronAPI.onResetView(() => {
        console.log("Received 'reset-view' message from main process")

        queryClient.removeQueries(["screenshots"])
        queryClient.removeQueries(["solution"])
        queryClient.removeQueries(["problem_statement"])
        setView("queue")
        console.log("View reset to 'queue' via Command+R shortcut")
      }),
      window.electronAPI.onProblemExtracted((data: any) => {
        if (view === "queue") {
          console.log("Problem extracted successfully")
          queryClient.invalidateQueries(["problem_statement"])
          queryClient.setQueryData(["problem_statement"], data)
        }
      })
    ]
    return () => cleanupFunctions.forEach((cleanup) => cleanup())
  }, [])

  return (
    <div ref={containerRef} className="min-h-0 flex flex-col items-center">
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          {view === "queue" ? (
            <Queue setView={setView} />
          ) : view === "solutions" ? (
            <Solutions setView={setView} />
          ) : view === "history" ? (
            <div className="bg-transparent w-full h-full flex flex-col">
              <div className="px-4 py-3 flex-shrink-0">
                <QueueCommands
                  screenshots={[]}
                  isLoading={false}
                  setView={setView}
                  onTooltipVisibilityChange={handleQueueCommandsTooltipVisibility}
                />
              </div>
              <div className="flex-1 px-4 pb-3 min-h-0">
                <AnalysisHistory setView={setView} />
              </div>
            </div>
          ) : (
            <></>
          )}
          <ToastViewport />
        </ToastProvider>
      </QueryClientProvider>
    </div>
  )
}

export default App
