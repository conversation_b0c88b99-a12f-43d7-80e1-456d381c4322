// Solutions.tsx
import React, { useState, useEffect, useRef } from "react"
import { useQuery, useQueryClient } from "react-query"
import { Prism as Syntax<PERSON>ighlighter } from "react-syntax-highlighter"
import { dracula } from "react-syntax-highlighter/dist/esm/styles/prism"
import ReactMarkdown from 'react-markdown'

import ScreenshotQueue from "../components/Queue/ScreenshotQueue"
import {
  Toast,
  ToastDescription,
  ToastMessage,
  ToastTitle,
  ToastVariant
} from "../components/ui/toast"
import { ProblemStatementData } from "../types/solutions"
import { AudioResult } from "../types/audio"
import SolutionCommands from "../components/Solutions/SolutionCommands"
import Debug from "./Debug"

// (Using global ElectronAPI type from src/types/electron.d.ts)

// Define a type for individual entities if not already globally available
// This matches the structure in ScreenshotAnalysisRecord
interface Entity {
  type: string;
  value: string;
}

// Define a type for Exa search results (simplified for now)
// interface ExaSearchResult { // REMOVE THIS INTERFACE
//   id: string; // Exa results have an id
//   url: string;
//   title?: string;
//   text?: string;
//   // Add other fields from Exa's actual response if needed e.g. publishedDate, score
// }

export const ContentSection = ({
  title,
  content,
  isLoading
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
}) => (
  <div className="space-y-2">
    <h2 className="text-[13px] font-medium text-white tracking-wide [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">
      {title}
    </h2>
    {isLoading ? (
      <div className="mt-4 flex">
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          Loading ....
        </p>
      </div>
    ) : (
      <div className="text-[13px] leading-[1.4] text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">
        {typeof content === 'string' ? (
          <div className="prose prose-sm prose-invert">
            <ReactMarkdown
              components={{
                // Reverting the strong style override for this test
                // strong: ({node, ...props}) => <strong style={{color: 'red'}} {...props} />
              }}
            >
        {content}
            </ReactMarkdown>
          </div>
        ) : (
          // For non-string content (e.g., 'Analysis' section).
          <div className="whitespace-pre-line">{content}</div>
        )}
      </div>
    )}
  </div>
)
const SolutionSection = ({
  title,
  content,
  isLoading
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
}) => (
  <div className="space-y-2">
    <h2 className="text-[13px] font-medium text-white tracking-wide [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">
      {title}
    </h2>
    {isLoading ? (
      <div className="space-y-1.5">
        <div className="mt-4 flex">
          <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
            Loading solutions...
          </p>
        </div>
      </div>
    ) : (
      <div className="w-full">
        <SyntaxHighlighter
          showLineNumbers
          language="python"
          style={dracula}
          customStyle={{
            maxWidth: "100%",
            margin: 0,
            padding: "1rem",
            whiteSpace: "pre-wrap",
            wordBreak: "break-all"
          }}
          wrapLongLines={true}
        >
          {content as string}
        </SyntaxHighlighter>
      </div>
    )}
  </div>
)

export const ComplexitySection = ({
  timeComplexity,
  spaceComplexity,
  isLoading
}: {
  timeComplexity: string | null
  spaceComplexity: string | null
  isLoading: boolean
}) => (
  <div className="space-y-2">
    <h2 className="text-[13px] font-medium text-white tracking-wide [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">
      Complexity (Updated)
    </h2>
    {isLoading ? (
      <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
        Calculating complexity...
      </p>
    ) : (
      <div className="space-y-1">
        <div className="flex items-start gap-2 text-[13px] leading-[1.4] text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">
          <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
          <div>
            <strong>Time:</strong> {timeComplexity}
          </div>
        </div>
        <div className="flex items-start gap-2 text-[13px] leading-[1.4] text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">
          <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
          <div>
            <strong>Space:</strong> {spaceComplexity}
          </div>
        </div>
      </div>
    )}
  </div>
)

interface SolutionsProps {
  setView: React.Dispatch<React.SetStateAction<"queue" | "solutions" | "debug" | "history">>
}
const Solutions: React.FC<SolutionsProps> = ({ setView }) => {
  const queryClient = useQueryClient()
  const contentRef = useRef<HTMLDivElement>(null)

  // Audio recording state
  const [audioRecording, setAudioRecording] = useState(false)
  const [audioResult, setAudioResult] = useState<AudioResult | null>(null)

  const [debugProcessing, setDebugProcessing] = useState(false)
  const [problemStatementData, setProblemStatementData] =
    useState<ProblemStatementData | null>(null)
  const [solutionData, setSolutionData] = useState<string | null>(null)
  const [thoughtsData, setThoughtsData] = useState<string[] | null>(null)
  const [timeComplexityData, setTimeComplexityData] = useState<string | null>(
    null
  )
  const [spaceComplexityData, setSpaceComplexityData] = useState<string | null>(
    null
  )
  const [customContent, setCustomContent] = useState<string | null>(null)

  // Follow-up question state
  const [followUpQuestion, setFollowUpQuestion] = useState("")
  const [followUpAnswer, setFollowUpAnswer] = useState<string | null>(null)
  const [isAskingFollowUp, setIsAskingFollowUp] = useState(false)

  // New state for loading indicator during problem extraction from image/audio
  const [isExtractingProblem, setIsExtractingProblem] = useState(false)

  // State for Exa API search // REMOVE EXA STATE
  // const [exaSearchResults, setExaSearchResults] = useState<ExaSearchResult[] | null>(null);
  // const [isSearchingWithExa, setIsSearchingWithExa] = useState(false);
  // const [exaSearchError, setExaSearchError] = useState<string | null>(null);
  // const [currentExaSearchTerm, setCurrentExaSearchTerm] = useState<string | null>(null);

  // New state for local person search service
  const [localServicePersonName, setLocalServicePersonName] = useState<string | null>(null);
  const [localServiceResult, setLocalServiceResult] = useState<string | null>(null);
  const [isCallingLocalService, setIsCallingLocalService] = useState(false);
  const [localServiceError, setLocalServiceError] = useState<string | null>(null);

  const [toastOpen, setToastOpen] = useState(false)
  const [toastMessage, setToastMessage] = useState<ToastMessage>({
    title: "",
    description: "",
    variant: "neutral"
  })

  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const [tooltipHeight, setTooltipHeight] = useState(0)

  const [isResetting, setIsResetting] = useState(false)

  const { data: extraScreenshots = [], refetch } = useQuery<Array<{ path: string; preview: string }>, Error>(
    ["extras"],
    async () => {
      try {
        const existing = await window.electronAPI.getScreenshots()
        return existing
      } catch (error) {
        console.error("Error loading extra screenshots:", error)
        return []
      }
    },
    {
      staleTime: Infinity,
      cacheTime: Infinity
    }
  )

  const showToast = (
    title: string,
    description: string,
    variant: ToastVariant
  ) => {
    setToastMessage({ title, description, variant })
    setToastOpen(true)
  }

  const handleDeleteExtraScreenshot = async (index: number) => {
    const screenshotToDelete = extraScreenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        refetch() // Refetch screenshots instead of managing state directly
      } else {
        console.error("Failed to delete extra screenshot:", response.error)
      }
    } catch (error) {
      console.error("Error deleting extra screenshot:", error)
    }
  }

  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        if (isTooltipVisible) {
          contentHeight += tooltipHeight
        }
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(() => refetch()),
      window.electronAPI.onResetView(() => {
        // Set resetting state first
        setIsResetting(true)

        // Clear the queries
        queryClient.removeQueries(["solution"])
        queryClient.removeQueries(["new_solution"])
        queryClient.removeQueries(["problem_statement"]); // Clear problem statement too on reset

        // Reset other states
        refetch()
        setIsExtractingProblem(false); // Reset loading state

        // After a small delay, clear the resetting state
        setTimeout(() => {
          setIsResetting(false)
        }, 0)
      }),
      // This event (onSolutionStart) is actually "initial-start" from main process
      window.electronAPI.onSolutionStart(async () => {
        // Reset UI state for a new solution
        setSolutionData(null)
        setThoughtsData(null)
        setTimeComplexityData(null)
        setSpaceComplexityData(null)
        setCustomContent(null)
        setAudioResult(null)
        // Indicate that problem extraction/initial processing has started
        setIsExtractingProblem(true);

        // Start audio recording from user's microphone
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
          const mediaRecorder = new MediaRecorder(stream)
          const chunks: Blob[] = []
          mediaRecorder.ondataavailable = (e) => chunks.push(e.data)
          mediaRecorder.start()
          setAudioRecording(true)
          // Record for 5 seconds (or adjust as needed)
          setTimeout(() => mediaRecorder.stop(), 5000)
          mediaRecorder.onstop = async () => {
            setAudioRecording(false)
            const blob = new Blob(chunks, { type: chunks[0]?.type || 'audio/webm' })
            const reader = new FileReader()
            reader.onloadend = async () => {
              const base64Data = (reader.result as string).split(',')[1]
              // Send audio to Gemini for analysis
              try {
                const result = await window.electronAPI.analyzeAudioFromBase64(
                  base64Data,
                  blob.type
                )
                // Store result in react-query cache
                queryClient.setQueryData(["audio_result"], result)
                setAudioResult(result)
              } catch (err) {
                console.error('Audio analysis failed:', err)
              }
            }
            reader.readAsDataURL(blob)
          }
        } catch (err) {
          console.error('Audio recording error:', err)
        }

        // Simulate receiving custom content shortly after start
        setTimeout(() => {
          setCustomContent(
            "This is the dynamically generated content appearing after loading starts."
          )
        }, 1500) // Example delay
      }),
      // Listener for when the problem statement has been extracted from image/audio
      window.electronAPI.onProblemExtracted((data) => {
        queryClient.setQueryData(["problem_statement"], data);
        setIsExtractingProblem(false);
      }),
      //if there was an error processing the initial solution
      window.electronAPI.onSolutionError((error: string) => {
        showToast(
          "Processing Failed",
          "There was an error processing your request.",
          "error"
        )
        // Reset solutions in the cache (even though this shouldn't ever happen) and complexities to previous states
        const solution = queryClient.getQueryData(["solution"]) as {
          code: string
          thoughts: string[]
          time_complexity: string
          space_complexity: string
        } | null
        
        // If there was no solution data and we were extracting, it implies an error during extraction
        if (!solution || isExtractingProblem) {
          setView("queue"); // Go back to queue if initial extraction fails
        }
        setIsExtractingProblem(false); // Stop loading indicator

        setSolutionData(solution?.code || null)
        setThoughtsData(solution?.thoughts || null)
        setTimeComplexityData(solution?.time_complexity || null)
        setSpaceComplexityData(solution?.space_complexity || null)
        console.error("Processing error:", error)
      }),
      //when the initial solution is generated, we'll set the solution data to that
      window.electronAPI.onSolutionSuccess((data) => {
        if (!data?.solution) {
          console.warn("Received empty or invalid solution data")
          return
        }

        console.log({ solution: data.solution })

        const solutionData = {
          code: data.solution.code,
          thoughts: data.solution.thoughts,
          time_complexity: data.solution.time_complexity,
          space_complexity: data.solution.space_complexity
        }

        queryClient.setQueryData(["solution"], solutionData)
        setSolutionData(solutionData.code || null)
        setThoughtsData(solutionData.thoughts || null)
        setTimeComplexityData(solutionData.time_complexity || null)
        setSpaceComplexityData(solutionData.space_complexity || null)
      }),

      //########################################################
      //DEBUG EVENTS
      //########################################################
      window.electronAPI.onDebugStart(() => {
        //we'll set the debug processing state to true and use that to render a little loader
        setDebugProcessing(true)
      }),
      //the first time debugging works, we'll set the view to debug and populate the cache with the data
      window.electronAPI.onDebugSuccess((data) => {
        console.log({ debug_data: data })

        queryClient.setQueryData(["new_solution"], data.solution)
        setDebugProcessing(false)
      }),
      //when there was an error in the initial debugging, we'll show a toast and stop the little generating pulsing thing.
      window.electronAPI.onDebugError(() => {
        showToast(
          "Processing Failed",
          "There was an error debugging your code.",
          "error"
        )
        setDebugProcessing(false)
      }),
      window.electronAPI.onProcessingNoScreenshots(() => {
        showToast(
          "No Screenshots",
          "There are no extra screenshots to process.",
          "neutral"
        )
      })
    ]

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [isTooltipVisible, tooltipHeight])

  useEffect(() => {
    setProblemStatementData(
      queryClient.getQueryData(["problem_statement"]) || null
    )
    setSolutionData(queryClient.getQueryData(["solution"]) || null)

    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event?.query.queryKey[0] === "problem_statement") {
        setProblemStatementData(
          queryClient.getQueryData(["problem_statement"]) || null
        )
        // If this is from audio processing, show it in the custom content section
        const audioResult = queryClient.getQueryData(["audio_result"]) as AudioResult | undefined;
        if (audioResult) {
          // Update all relevant sections when audio result is received
          setProblemStatementData({
            problem_statement: audioResult.text,
            input_format: {
              description: "Generated from audio input",
              parameters: []
            },
            output_format: {
              description: "Generated from audio input",
              type: "string",
              subtype: "text"
            },
            complexity: {
              time: "N/A",
              space: "N/A"
            },
            test_cases: [],
            validation_type: "manual",
            difficulty: "custom"
          });
          setSolutionData(null); // Reset solution to trigger loading state
          setThoughtsData(null);
          setTimeComplexityData(null);
          setSpaceComplexityData(null);
        }
      }
      if (event?.query.queryKey[0] === "solution") {
        const solution = queryClient.getQueryData(["solution"]) as {
          code: string
          thoughts: string[]
          time_complexity: string
          space_complexity: string
        } | null

        setSolutionData(solution?.code ?? null)
        setThoughtsData(solution?.thoughts ?? null)
        setTimeComplexityData(solution?.time_complexity ?? null)
        setSpaceComplexityData(solution?.space_complexity ?? null)
      }
    })
    return () => unsubscribe()
  }, [queryClient])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    setIsTooltipVisible(visible)
    setTooltipHeight(height)
  }

  const handleAskFollowUp = async () => {
    let previousAnalysisText = "";
    if (problemStatementData?.problem_statement) {
      if (typeof problemStatementData.problem_statement === 'string') {
        previousAnalysisText = problemStatementData.problem_statement;
      } else if (problemStatementData.output_format?.subtype === 'image_analysis_details' && problemStatementData.problem_statement.rawResponse) {
        previousAnalysisText = problemStatementData.problem_statement.rawResponse;
      }
    }

    if (!previousAnalysisText || !followUpQuestion.trim()) {
      showToast(
        "Missing Information",
        "Please ensure there is a previous analysis and a follow-up question.",
        "neutral"
      )
      return
    }

    setIsAskingFollowUp(true)
    setFollowUpAnswer(null)
    try {
      const result = await window.electronAPI.askFollowUpOnScreenshotResult(
        previousAnalysisText,
        followUpQuestion
      )
      setFollowUpAnswer(result.text)
      setFollowUpQuestion("") // Clear input after successful submission
    } catch (error) {
      console.error("Error asking follow-up question:", error)
      showToast(
        "Error",
        "Failed to get an answer to the follow-up question.",
        "error"
      )
    } finally {
      setIsAskingFollowUp(false)
    }
  }

  const handlePersonEntityClick = async (entityValue: string) => {
    // Ensure problemStatementData and its recordId are available
    if (!problemStatementData || typeof problemStatementData !== 'object' || !problemStatementData.recordId) {
      console.error("Cannot perform person search: recordId is missing from problemStatementData.");
      setLocalServiceError("Cannot perform person search: missing critical record information.");
      return;
    }
    const recordId = problemStatementData.recordId as string; // Type assertion after check

    setLocalServicePersonName(entityValue);
    setIsCallingLocalService(true);
    setLocalServiceError(null);
    setLocalServiceResult(null);

    try {
      const response = await window.electronAPI.searchPersonLocal({ personName: entityValue, recordId });
      if (response.success && typeof response.data === 'string') {
        setLocalServiceResult(response.data);
      } else {
        setLocalServiceError(response.error || "Failed to fetch results from the local service.");
        setLocalServiceResult(null);
      }
      if (response.historyError) {
        console.warn("[SolutionsPage] History saving issue for person search:", response.historyError);
        // Optionally, show a non-blocking toast or small message to the user about history saving issues
      }
    } catch (error: any) {
      console.error("Error calling local service:", error);
      setLocalServiceError(error.message || "An unexpected error occurred with the local service.");
      setLocalServiceResult(null);
    } finally {
      setIsCallingLocalService(false);
    }
  };

  // Helper to determine if follow-up can be asked
  const canAskFollowUp = () => {
    if (!problemStatementData || isExtractingProblem) return false;
    if (typeof problemStatementData.problem_statement === 'string') {
      return !!problemStatementData.problem_statement;
    }
    if (problemStatementData.output_format?.subtype === 'image_analysis_details') {
      return !!problemStatementData.problem_statement.rawResponse;
    }
    return false;
  };

  return (
    <>
      {!isResetting && queryClient.getQueryData(["new_solution"]) ? (
        <>
          <Debug
            isProcessing={debugProcessing}
            setIsProcessing={setDebugProcessing}
          />
        </>
      ) : (
        <div ref={contentRef} className="bg-transparent w-full">
          <div className="px-4 py-3">
            <Toast
              open={toastOpen}
              onOpenChange={setToastOpen}
              variant={toastMessage.variant}
              duration={3000}
            >
              <ToastTitle>{toastMessage.title}</ToastTitle>
              <ToastDescription>{toastMessage.description}</ToastDescription>
            </Toast>

            <div className="space-y-3 w-full">
              {/* Conditionally render the screenshot queue if solutionData is available */}
              {solutionData && (
                <ScreenshotQueue
                  isLoading={debugProcessing}
                  screenshots={extraScreenshots}
                  onDeleteScreenshot={handleDeleteExtraScreenshot}
                />
              )}

              {/* Navbar of commands with the SolutionsHelper */}
              <SolutionCommands
                extraScreenshots={extraScreenshots}
                onTooltipVisibilityChange={handleTooltipVisibilityChange}
              />

              {/* Main Content - Modified width constraints */}
              <div className="w-full text-sm text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] bg-white/25 backdrop-blur-lg rounded-lg border border-white/20 shadow-lg">
            <div className="rounded-lg overflow-hidden">
              <div className="px-4 py-3 space-y-4 max-w-full">
                {/* Show Screenshot or Audio Result as main output if validation_type is manual */}
                {problemStatementData?.validation_type === "manual" ? (
                  <>
                    {problemStatementData.output_format?.subtype === "image_analysis_details" && 
                     typeof problemStatementData.problem_statement === 'object' ? (
                      // Special rendering for image_analysis_details
                      <>
                        <ContentSection
                          title="Screenshot Analysis"
                          content={problemStatementData.problem_statement.description}
                          isLoading={isExtractingProblem || !problemStatementData}
                        />
                        
                        {/* Entities Section */}
                        {problemStatementData.problem_statement.entities && problemStatementData.problem_statement.entities.length > 0 ? (
                          <div className="mt-3 space-y-1">
                            <h3 className="text-[13px] font-medium text-white tracking-wide [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">Identified Entities:</h3>
                            <ul className="list-disc list-inside pl-1 space-y-0.5 text-xs">
                              {problemStatementData.problem_statement.entities.map((entity: Entity, index: number) => (
                                <li key={index}>
                                  <span className="font-semibold">{entity.type}:</span>{' '}
                                  {entity.type === "Person" ? (
                                    <button
                                      onClick={() => handlePersonEntityClick(entity.value)}
                                      className="text-blue-300 hover:text-blue-200 underline disabled:opacity-50 disabled:cursor-not-allowed ml-1"
                                      disabled={isCallingLocalService && localServicePersonName === entity.value}
                                      title={`Search local service for "${entity.value}"`}
                                    >
                                      {entity.value}
                                      {(isCallingLocalService && localServicePersonName === entity.value) && <span className="ml-1 animate-pulse">...</span>}
                                    </button>
                                  ) : (
                                    entity.value
                                  )}
                                </li>
                              ))}
                            </ul>
                          </div>
                        ) : (
                            <p className="text-xs italic mt-1">No specific entities identified.</p>
                        )}

                        {/* New Local Service Search Results Section */}
                        {localServicePersonName && (
                          <div className="mt-4 p-3 bg-black/20 rounded-lg border border-white/10">
                            <h4 className="text-[13px] font-medium text-white mb-2">
                              Local Service Search for: "{localServicePersonName}"
                            </h4>
                           \s
                            {isCallingLocalService && <p className="text-xs text-white/70 animate-pulse">Searching with local service...</p>}
                            {localServiceError && <p className="text-xs text-red-400">Error: {localServiceError}</p>}
                           \s
                            {localServiceResult && !isCallingLocalService && (
                              <div className="space-y-2 text-xs max-h-60 overflow-y-auto pr-2">
                                <pre className="whitespace-pre-wrap text-white/90 text-[11px] leading-relaxed">{localServiceResult}</pre>
                              </div>
                            )}
                            {localServiceResult === "" && !isCallingLocalService && !localServiceError && ( // Handle empty string result
                              <p className="text-xs text-white/70">No results found for "{localServicePersonName}" or service returned empty.</p>
                            )}
                          </div>
                        )}
                      </>
                    ) : (
                      // Fallback for audio or other manual validation types that use string problem_statement
                      <ContentSection
                        title={problemStatementData?.output_format?.subtype === "voice" ? "Audio Result" : "Screenshot Result"}
                        content={typeof problemStatementData.problem_statement === 'string' ? problemStatementData.problem_statement : "Loading analysis..."}
                        isLoading={isExtractingProblem || !problemStatementData}
                      />
                    )}

                    {/* Follow-up question section */}
                    {canAskFollowUp() && (
                      <div className="mt-4 space-y-2">
                        <h3 className="text-[13px] font-medium text-white tracking-wide [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">Ask a follow-up:</h3>
                        {/* New input group wrapper */}
                        <div className="flex items-stretch bg-black/40 backdrop-blur-sm rounded-lg border border-white/20 focus-within:ring-2 focus-within:ring-blue-400 focus-within:border-transparent overflow-hidden transition-all duration-150">
                          <textarea
                            value={followUpQuestion}
                            onChange={(e) => setFollowUpQuestion(e.target.value)}
                            placeholder="Type your follow-up question here..."
                            className="flex-grow p-2.5 bg-transparent text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] placeholder-white/50 focus:outline-none resize-none"
                            rows={2}
                            disabled={isAskingFollowUp}
                          />
                          <button
                            onClick={handleAskFollowUp}
                            disabled={isAskingFollowUp || !followUpQuestion.trim()}
                            className="px-4 bg-white/10 hover:bg-white/20 text-white focus:outline-none transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                          >
                            {isAskingFollowUp ? "Asking..." : "Ask"}
                          </button>
                        </div>
                        {isAskingFollowUp && (
                          <p className="text-xs text-white/70 animate-pulse">Waiting for answer...</p>
                        )}
                        {followUpAnswer && (
                          <div className="mt-2 p-3 bg-white/25 backdrop-blur-lg rounded-lg border border-white/20 shadow-lg">
                             <h4 className="text-[13px] font-medium text-white mb-1 [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">Answer:</h4>
                            <p className="text-[13px] leading-[1.4] text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] whitespace-pre-line">{followUpAnswer}</p>
                          </div>
                        )}
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    {/* Problem Statement Section - Only for non-manual */}
                    <ContentSection
                      title={problemStatementData?.output_format?.subtype === "voice" ? "Voice Input" : "Problem Statement"}
                      content={typeof problemStatementData?.problem_statement === 'string' ? problemStatementData.problem_statement : "Problem details are structured or not yet loaded."}
                      isLoading={!problemStatementData}
                    />
                    {/* Show loading state when waiting for solution */}
                    {problemStatementData && !solutionData && !isExtractingProblem && (
                      <div className="mt-4 flex">
                        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
                          {problemStatementData?.output_format?.subtype === "voice" 
                            ? "Processing voice input..." 
                            : "Generating solutions..."}
                        </p>
                      </div>
                    )}
                    {/* Solution Sections (legacy, only for non-manual) */}
                    {solutionData && (
                      <>
                        <ContentSection
                          title="Analysis"
                          content={
                            thoughtsData && (
                              <div className="space-y-3">
                                <div className="space-y-1">
                                  {thoughtsData.map((thought, index) => (
                                    <div
                                      key={index}
                                      className="flex items-start gap-2"
                                    >
                                      <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
                                      <div>{thought}</div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )
                          }
                          isLoading={!thoughtsData}
                        />
                        <SolutionSection
                          title={problemStatementData?.output_format?.subtype === "voice" ? "Response" : "Solution"}
                          content={solutionData}
                          isLoading={!solutionData}
                        />
                        {problemStatementData?.output_format?.subtype !== "voice" && (
                          <ComplexitySection
                            timeComplexity={timeComplexityData}
                            spaceComplexity={spaceComplexityData}
                            isLoading={!timeComplexityData || !spaceComplexityData}
                          />
                        )}
                      </>
                    )}
                  </>
                )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default Solutions
