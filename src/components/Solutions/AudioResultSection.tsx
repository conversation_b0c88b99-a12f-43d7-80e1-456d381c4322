import React, { useState } from 'react';
import { Volume2, Clock, ChevronDown, ChevronUp, FileText } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import KnowledgeGraph from '../shared/KnowledgeGraph';

interface AudioResultSectionProps {
  content: string;
  isLoading: boolean;
  entities?: Array<{ type: string; value: string }>;
  rawResponse?: string;
  timestamp?: number;
}

const AudioResultSection: React.FC<AudioResultSectionProps> = ({ 
  content, 
  isLoading, 
  entities = [],
  rawResponse,
  timestamp 
}) => {
  const [showRawResponse, setShowRawResponse] = useState(false);

  if (isLoading) {
    return (
      <div className="space-y-2">
        <h2 className="text-[13px] font-medium text-white tracking-wide [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] flex items-center gap-2">
          <Volume2 size={14} className="text-blue-300" />
          Audio Analysis Result
        </h2>
        <div className="mt-4 flex">
          <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
            Processing audio input...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-[13px] font-medium text-white tracking-wide [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] flex items-center gap-2">
          <Volume2 size={14} className="text-blue-300" />
          Audio Analysis Result
        </h2>
        {timestamp && (
          <div className="flex items-center gap-1 text-xs text-white/60">
            <Clock size={12} />
            <span>{new Date(timestamp).toLocaleTimeString()}</span>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="bg-white/10 rounded-lg p-4 border border-white/10">
        <div className="prose prose-sm prose-invert max-w-none">
          <ReactMarkdown>
            {content}
          </ReactMarkdown>
        </div>
      </div>

      {/* Entity Knowledge Graph */}
      {entities && entities.length > 0 && (
        <KnowledgeGraph 
          entities={entities}
          recordId={`audio-${timestamp || Date.now()}`}
          timestamp={timestamp}
          title="Identified Entities"
          showStats={true}
        />
      )}

      {/* Raw Response - Expandable */}
      {rawResponse && (
        <div>
          <button
            onClick={() => setShowRawResponse(!showRawResponse)}
            className="flex items-center gap-2 mb-2 hover:text-white/80 transition-colors text-[13px] font-medium"
          >
            <FileText size={14} />
            <span>Raw Audio Analysis:</span>
            {showRawResponse ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
          </button>
          {showRawResponse && (
            <div className="bg-black/20 rounded-lg p-3 border border-white/10">
              <pre className="whitespace-pre-wrap text-[11px] leading-relaxed text-white/90 overflow-x-auto">
                {rawResponse}
              </pre>
            </div>
          )}
        </div>
      )}

      {/* Processing Status */}
      <div className="flex items-center justify-between text-xs text-white/60 pt-2 border-t border-white/10">
        <span>Audio processing complete</span>
        <span className="text-green-400">✓ Ready for next steps</span>
      </div>
    </div>
  );
};

export default AudioResultSection;
