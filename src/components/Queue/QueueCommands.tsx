import React, { useState, useEffect, useRef } from "react"
import { IoLogOutOutline } from "react-icons/io5"
import { Camera, Trash2, RefreshCw, LogOut, HelpCircle, Settings, Mic, StopCircle, X, History } from 'lucide-react'
import ReactMarkdown from 'react-markdown'

interface QueueCommandsProps {
  onTooltipVisibilityChange: (visible: boolean, height: number) => void
  screenshots: Array<{ path: string; preview: string }>
  isLoading?: boolean
  setView: React.Dispatch<React.SetStateAction<"queue" | "solutions" | "debug" | "history">>
}

const QueueCommands: React.FC<QueueCommandsProps> = ({
  onTooltipVisibilityChange,
  screenshots,
  isLoading,
  setView
}) => {
  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const tooltipRef = useRef<HTMLDivElement>(null)
  const [isRecording, setIsRecording] = useState(false)
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)
  const [audioResult, setAudioResult] = useState<string | null>(null)
  const chunks = useRef<Blob[]>([])

  useEffect(() => {
    let tooltipHeight = 0
    if (tooltipRef.current && isTooltipVisible) {
      tooltipHeight = tooltipRef.current.offsetHeight + 10
    }
    onTooltipVisibilityChange(isTooltipVisible, tooltipHeight)
  }, [isTooltipVisible, onTooltipVisibilityChange])

  // Listen for global reset event to clear audioResult
  useEffect(() => {
    const cleanup = window.electronAPI.onResetView(() => {
      setAudioResult(null);
    });
    return () => cleanup();
  }, []);

  const handleMouseEnter = () => {
    setIsTooltipVisible(true)
  }

  const handleMouseLeave = () => {
    setIsTooltipVisible(false)
  }

  const handleRecordClick = async () => {
    if (!isRecording) {
      // Start recording
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        const recorder = new MediaRecorder(stream)
        recorder.ondataavailable = (e) => chunks.current.push(e.data)
        recorder.onstop = async () => {
          const blob = new Blob(chunks.current, { type: chunks.current[0]?.type || 'audio/webm' })
          chunks.current = []
          const reader = new FileReader()
          reader.onloadend = async () => {
            const base64Data = (reader.result as string).split(',')[1]
            try {
              const result = await window.electronAPI.analyzeAudioFromBase64(base64Data, blob.type)
              setAudioResult(result.text)
            } catch (err) {
              setAudioResult('Audio analysis failed.')
            }
          }
          reader.readAsDataURL(blob)
        }
        setMediaRecorder(recorder)
        recorder.start()
        setIsRecording(true)
      } catch (err) {
        setAudioResult('Could not start recording.')
      }
    } else {
      // Stop recording
      mediaRecorder?.stop()
      setIsRecording(false)
      setMediaRecorder(null)
    }
  }

  return (
    <div className="w-full">
      <div className="w-full text-xs text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] backdrop-blur-lg bg-white/25 rounded-lg border border-white/20 shadow-lg py-2 px-4 flex items-center justify-between gap-x-2 min-w-0">
        {/* Left and Middle Commands Group */}
        <div className="flex items-center gap-x-2 flex-1 min-w-0">
          {/* Show/Hide */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <span className="text-[11px] leading-none whitespace-nowrap">Show/Hide</span>
            <div className="flex gap-1">
              <button className="bg-white/10 hover:bg-white/20 transition-colors rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                ⌘
              </button>
              <button className="bg-white/10 hover:bg-white/20 transition-colors rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                B
              </button>
            </div>
          </div>

          {/* Screenshot */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <span className="text-[11px] leading-none truncate">
              {screenshots.length === 0 ? "Take first screenshot" : "Screenshot"}
            </span>
            <div className="flex gap-1">
              <button className="bg-white/10 hover:bg-white/20 transition-colors rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                ⌘
              </button>
              <button className="bg-white/10 hover:bg-white/20 transition-colors rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                H
              </button>
            </div>
          </div>

          {/* Solve Command - Conditionally render if no audioResult */}
          {!audioResult && screenshots.length > 0 && (
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="text-[11px] leading-none whitespace-nowrap">Solve</span>
              <div className="flex gap-1">
                <button className="bg-white/10 hover:bg-white/20 transition-colors rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                  ⌘
                </button>
                <button className="bg-white/10 hover:bg-white/20 transition-colors rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                  ↵
                </button>
              </div>
            </div>
          )}

          {/* Start Over Hint - Conditionally render if audioResult is present */}
          {audioResult && (
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="text-[11px] leading-none whitespace-nowrap">Start Over</span>
              <div className="flex gap-1">
                <button className="bg-white/10 hover:bg-white/20 transition-colors rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                  ⌘
                </button>
                <button className="bg-white/10 hover:bg-white/20 transition-colors rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                  R
                </button>
              </div>
            </div>
          )}

          {/* View History Button - Icon only, using History icon */}
          <button
            onClick={() => setView('history')}
            className={`flex items-center justify-center rounded-md p-1.5 text-xs transition-colors flex-shrink-0
              bg-purple-500/20 hover:bg-purple-500/30 text-purple-300
              disabled:opacity-50 disabled:cursor-not-allowed`}
            title="View Analysis History"
            disabled={isLoading || false}
          >
            <History size={16} />
          </button>

          {/* Audio Input Button */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <button
              onClick={handleRecordClick}
              className={`flex items-center justify-center gap-1.5 rounded-md px-3 py-1.5 text-xs transition-colors 
                ${isRecording ? 'bg-red-500/80 hover:bg-red-500/90 text-white' : 'bg-blue-500/20 hover:bg-blue-500/30 text-blue-300'}
                disabled:opacity-50 disabled:cursor-not-allowed`}
              title={isRecording ? "Stop Recording" : "Record Voice Input"}
              disabled={isLoading || false}
            >
              {isRecording ? (
                <>
                  <StopCircle size={14} className="animate-pulse" />
                  <span>Stop</span>
                </>
              ) : (
                <Mic size={14} />
              )}
            </button>
          </div>
        </div>

        {/* Right Aligned Controls Group (Help, Logout) */}
        <div className="flex items-center gap-x-2 flex-shrink-0">
          {/* Question mark with tooltip */}
          <div
            className="relative inline-block flex-shrink-0"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <div className="w-6 h-6 rounded-full bg-white/10 hover:bg-white/20 backdrop-blur-sm transition-colors flex items-center justify-center cursor-help z-10">
              <span className="text-xs text-white/70">?</span>
            </div>

            {/* Tooltip Content */}
            {isTooltipVisible && (
              <div
                ref={tooltipRef}
                className="absolute top-full right-0 mt-2 w-80 z-50 max-w-[calc(100vw-2rem)]"
              >
                <div className="p-3 text-xs bg-black/80 backdrop-blur-md rounded-lg border border-white/10 text-white/90 shadow-lg">
                  <div className="space-y-4">
                    <h3 className="font-medium truncate">Keyboard Shortcuts</h3>
                    <div className="space-y-3">
                      {/* Toggle Command */}
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="truncate whitespace-nowrap">Toggle Window</span>
                          <div className="flex gap-1 flex-shrink-0">
                            <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                              ⌘
                            </span>
                            <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                              B
                            </span>
                          </div>
                        </div>
                        <p className="text-[10px] leading-relaxed text-white/70 truncate">
                          Show or hide this window.
                        </p>
                      </div>
                      {/* Screenshot Command */}
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="truncate whitespace-nowrap">Take Screenshot</span>
                          <div className="flex gap-1 flex-shrink-0">
                            <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                              ⌘
                            </span>
                            <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                              H
                            </span>
                          </div>
                        </div>
                        <p className="text-[10px] leading-relaxed text-white/70 truncate">
                          Take a screenshot of the problem description. The tool
                          will extract and analyze the problem. The 5 latest
                          screenshots are saved.
                        </p>
                      </div>

                      {/* Solve Command */}
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="truncate whitespace-nowrap">Solve Problem</span>
                          <div className="flex gap-1 flex-shrink-0">
                            <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                              ⌘
                            </span>
                            <span className="bg-white/10 px-1.5 py-0.5 rounded text-[10px] leading-none">
                              ↵
                            </span>
                          </div>
                        </div>
                        <p className="text-[10px] leading-relaxed text-white/70 truncate">
                          Generate a solution based on the current problem.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Separator */}
          <div className="h-4 w-px bg-white/20 flex-shrink-0" />

          {/* Sign Out Button - Moved to end */}
          <button
            className="text-red-500/70 hover:text-red-500/90 transition-colors hover:cursor-pointer flex-shrink-0"
            title="Sign Out"
            onClick={() => window.electronAPI.quitApp()}
          >
            <IoLogOutOutline className="w-4 h-4" />
          </button>
        </div>
      </div>
      {/* Audio Result Display */}
      {audioResult && (
        <div className="mt-3 p-3 bg-white/25 backdrop-blur-lg rounded-lg border border-white/20 text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] shadow-lg max-h-40 overflow-y-auto relative">
          <button 
            onClick={() => setAudioResult(null)}
            className="absolute top-2 right-2 p-1 bg-white/10 hover:bg-white/20 rounded-full transition-colors z-10"
          >
            <X size={12} />
          </button>
          <span className="font-semibold not-prose block mb-1">Audio Result:</span> 
          <ReactMarkdown>
            {audioResult}
          </ReactMarkdown>
        </div>
      )}
    </div>
  )
}

export default QueueCommands
