import React, { useState, useEffect } from 'react';
import { ArrowLeft, Trash2 } from 'lucide-react';
import HistoryItemDisplay from './HistoryItemDisplay';

interface ScreenshotAnalysisRecord {
  id: string;
  timestamp: number;
  imagePath: string;
  description: string;
  entities: Array<{ type: string; value: string }>;
  rawResponse: string;
  text?: string;
}

interface AnalysisHistoryProps {
  setView: React.Dispatch<React.SetStateAction<"queue" | "solutions" | "debug" | "history">>;
}

const AnalysisHistory: React.FC<AnalysisHistoryProps> = ({ setView }) => {
  const [history, setHistory] = useState<ScreenshotAnalysisRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [isClearing, setIsClearing] = useState(false);

  useEffect(() => {
    const fetchHistory = async () => {
      console.log("[AnalysisHistory] Fetching history...");
      try {
        setIsLoading(true);
        // @ts-ignore - Keeping for now if type resolution is still pending for user
        const result = await window.electronAPI.getAnalysisHistory();
        console.log("[AnalysisHistory] Result from getAnalysisHistory:", result);

        if (result.success) {
          setHistory(result.history.sort((a: ScreenshotAnalysisRecord, b: ScreenshotAnalysisRecord) => b.timestamp - a.timestamp));
          setError(null); // Clear any previous error
        } else {
          setError(result.error || 'Failed to fetch history.');
          console.error("[AnalysisHistory] Error fetching history from API:", result.error);
          setHistory([]); // Clear history on error
        }
      } catch (err: any) {
        console.error("[AnalysisHistory] Exception in fetchHistory:", err);
        setError(err.message || 'An unexpected error occurred.');
        setHistory([]); // Clear history on exception
      } finally {
        setIsLoading(false);
        console.log("[AnalysisHistory] Fetching complete. Loading state:", false);
      }
    };

    fetchHistory();
  }, []);

  const handleDeleteRecord = async (recordId: string) => {
    if (!confirm('Are you sure you want to delete this analysis record?')) {
      return;
    }

    setIsDeleting(recordId);
    try {
      // @ts-ignore
      const result = await window.electronAPI.deleteAnalysisRecord(recordId);
      if (result.success) {
        setHistory(prev => prev.filter(record => record.id !== recordId));
      } else {
        console.error('Failed to delete record:', result.error);
        alert('Failed to delete record: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting record:', error);
      alert('Error deleting record');
    } finally {
      setIsDeleting(null);
    }
  };

  const handleClearAll = async () => {
    if (!confirm('Are you sure you want to clear all analysis history? This action cannot be undone.')) {
      return;
    }

    setIsClearing(true);
    try {
      // @ts-ignore
      const result = await window.electronAPI.clearAnalysisHistory();
      if (result.success) {
        setHistory([]);
      } else {
        console.error('Failed to clear history:', result.error);
        alert('Failed to clear history: ' + result.error);
      }
    } catch (error) {
      console.error('Error clearing history:', error);
      alert('Error clearing history');
    } finally {
      setIsClearing(false);
    }
  };

  console.log("[AnalysisHistory] Rendering - isLoading:", isLoading, "Error:", error, "History items:", history.length);

  // Wrapper for the entire component, using flex to manage height
  // This component will be placed inside a flex item by App.tsx, so it can expand
  // Adding w-full to ensure it takes the width provided by App.tsx's centered column layout
  return (
    <div className="w-full flex flex-col flex-grow min-h-0"> {/* Flex column, flex-grow, min-h-0 for proper scroll parent */} 
      {/* Header section - fixed height */}
      <div className="flex justify-between items-center mb-3 flex-shrink-0 p-1"> {/* Added p-1 for slight padding */}
        <h2 className="text-lg font-semibold text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)]">
          Analysis History ({history.length})
        </h2>
        <div className="flex items-center gap-2">
          {history.length > 0 && (
            <button
              onClick={handleClearAll}
              disabled={isClearing}
              className="flex items-center gap-2 bg-red-500/20 hover:bg-red-500/30 text-red-300 text-xs px-3 py-1.5 rounded-md transition-colors border border-red-500/30 shadow-sm disabled:opacity-50"
            >
              <Trash2 size={14} />
              {isClearing ? 'Clearing...' : 'Clear All'}
            </button>
          )}
          <button
            onClick={() => setView('queue')}
            className="flex items-center gap-2 bg-white/10 hover:bg-white/20 text-white text-xs px-3 py-1.5 rounded-md transition-colors [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] border border-white/20 shadow-sm"
          >
            <ArrowLeft size={14} />
            Back to Queue
          </button>
        </div>
      </div>

      {/* Content section - scrolls if needed */}
      {isLoading && (
        <div className="flex-grow p-4 text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] animate-pulse flex items-center justify-center">
          Loading history...
        </div>
      )}
      {error && !isLoading && (
        <div className="flex-grow p-4 text-red-400 [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] flex items-center justify-center">
          Error loading history: {error}
        </div>
      )}
      {!isLoading && !error && history.length === 0 && (
        <div className="flex-grow p-4 text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] flex items-center justify-center">
          No analysis history found.
        </div>
      )}
      {!isLoading && !error && history.length > 0 && (
        <div className="flex-grow overflow-y-auto space-y-3 pr-1"> {/* List container: flex-grow, scroll, space between items */}
          {history.map((record) => (
            <HistoryItemDisplay
              key={record.id}
              record={record}
              onDelete={handleDeleteRecord}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default AnalysisHistory; 