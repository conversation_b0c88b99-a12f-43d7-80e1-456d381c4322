import React, { useState, useEffect } from 'react';
import { ArrowLeft } from 'lucide-react';

interface ScreenshotAnalysisRecord {
  id: string;
  timestamp: number;
  imagePath: string;
  description: string;
  entities: Array<{ type: string; value: string }>;
  rawResponse: string;
  text?: string;
}

interface AnalysisHistoryProps {
  setView: React.Dispatch<React.SetStateAction<"queue" | "solutions" | "debug" | "history">>;
}

const AnalysisHistory: React.FC<AnalysisHistoryProps> = ({ setView }) => {
  const [history, setHistory] = useState<ScreenshotAnalysisRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchHistory = async () => {
      try {
        setIsLoading(true);
        // @ts-ignore
        const result = await window.electronAPI.getAnalysisHistory();
        
        if (result.success) {
          setHistory(result.history || []);
        } else {
          console.error('Failed to fetch history:', result.error);
          setHistory([]);
        }
      } catch (error) {
        console.error('Error fetching history:', error);
        setHistory([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchHistory();
  }, []);

  return (
    <div className="w-full text-sm text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] bg-white/25 backdrop-blur-lg rounded-lg border border-white/20 shadow-lg">
      <div className="rounded-lg overflow-hidden">
        <div className="px-4 py-3 space-y-4 max-w-full">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">Analysis History ({history.length})</h2>
            <button
              onClick={() => setView('queue')}
              className="flex items-center gap-2 bg-white/10 hover:bg-white/20 text-white text-xs px-3 py-1.5 rounded-md transition-colors border border-white/20"
            >
              <ArrowLeft size={14} />
              Back to Queue
            </button>
          </div>

          {isLoading ? (
            <div className="text-center py-8">Loading history...</div>
          ) : history.length === 0 ? (
            <div className="text-center py-8">No analysis history found.</div>
          ) : (
            <div className="space-y-3">
              {history.map((record) => (
                <div key={record.id} className="bg-white/10 rounded-lg p-3 border border-white/10">
                  <div className="text-xs text-white/80 mb-2">
                    {new Date(record.timestamp).toLocaleString()}
                  </div>
                  <div className="text-sm mb-2">
                    {record.description || 'No description'}
                  </div>
                  {record.entities && record.entities.length > 0 && (
                    <div className="text-xs">
                      <div className="text-white/80 mb-1">Entities:</div>
                      <div className="flex flex-wrap gap-1">
                        {record.entities.map((entity, idx) => (
                          <span key={idx} className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded text-[10px]">
                            {entity.type}: {entity.value}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalysisHistory;
