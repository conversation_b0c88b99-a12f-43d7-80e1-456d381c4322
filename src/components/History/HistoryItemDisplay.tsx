import React, { useState } from 'react';
import { ChevronDown, ChevronUp, FileText, Trash2 } from 'lucide-react';
import KnowledgeGraph from '../shared/KnowledgeGraph';

// Assuming ScreenshotAnalysisRecord is defined in a shared types file or passed correctly
// For now, let's redefine it here or ensure it's imported if globally available
interface ScreenshotAnalysisRecord {
  id: string;
  timestamp: number;
  imagePath: string;
  description: string;
  entities: Array<{ type: string; value: string }>;
  rawResponse: string;
  text?: string;
  personSearches?: Array<{
    personName: string;
    serviceResponse: string;
    timestamp: number;
  }>;
}

interface HistoryItemDisplayProps {
  record: ScreenshotAnalysisRecord;
  onDelete?: (recordId: string) => void;
}

const HistoryItemDisplay: React.FC<HistoryItemDisplayProps> = ({ record, onDelete }) => {
  const formattedTimestamp = new Date(record.timestamp).toLocaleString();

  // State for expandable sections
  const [showRawResponse, setShowRawResponse] = useState(false);
  const [showPersonSearches, setShowPersonSearches] = useState(false);



  return (
    <div className="bg-white/25 backdrop-blur-lg rounded-lg border border-white/20 shadow-lg p-4 text-sm text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] space-y-3">
      {/* Title Section - Using timestamp */}
      <div className="flex justify-between items-center border-b border-white/20 pb-2 mb-2">
        <h3 className="text-[13px] font-medium tracking-wide">
          Analyzed: {formattedTimestamp} (ID: {record.id.substring(0,6)})
        </h3>
        {onDelete && (
          <button
            onClick={() => onDelete(record.id)}
            className="text-red-400 hover:text-red-300 transition-colors p-1 rounded hover:bg-red-500/10"
            title="Delete this record"
          >
            <Trash2 size={14} />
          </button>
        )}
      </div>
      
      {/* Content Area */}
      <div className="space-y-3 text-xs">


        {/* Description */}
        <div>
          <p className="font-semibold mb-0.5 text-[13px]">Description:</p>
          <p className="whitespace-pre-wrap text-xs">{record.description || "N/A"}</p>
        </div>

        {/* Knowledge Graph */}
        <KnowledgeGraph
          entities={record.entities}
          recordId={record.id}
          timestamp={record.timestamp}
          showStats={true}
        />

        {/* Raw Response - Expandable */}
        {record.rawResponse && (
          <div>
            <button
              onClick={() => setShowRawResponse(!showRawResponse)}
              className="flex items-center gap-2 mb-1 hover:text-white/80 transition-colors"
            >
              <FileText size={14} />
              <p className="font-semibold">Raw Response:</p>
              {showRawResponse ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
            </button>
            {showRawResponse && (
              <div className="bg-black/20 rounded p-2 border border-white/10">
                <pre className="whitespace-pre-wrap text-[11px] leading-relaxed text-white/90 overflow-x-auto">
                  {record.rawResponse}
                </pre>
              </div>
            )}
          </div>
        )}

        {/* Person Searches - Expandable */}
        {record.personSearches && record.personSearches.length > 0 && (
          <div>
            <button
              onClick={() => setShowPersonSearches(!showPersonSearches)}
              className="flex items-center gap-2 mb-1 hover:text-white/80 transition-colors"
            >
              <Users size={14} />
              <p className="font-semibold">Person Searches ({record.personSearches.length}):</p>
              {showPersonSearches ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
            </button>
            {showPersonSearches && (
              <div className="space-y-2">
                {record.personSearches.map((search, index) => (
                  <div key={index} className="bg-black/20 rounded p-2 border border-white/10">
                    <div className="flex justify-between items-center mb-1">
                      <p className="font-medium text-blue-300">{search.personName}</p>
                      <p className="text-[10px] text-white/60">
                        {new Date(search.timestamp).toLocaleString()}
                      </p>
                    </div>
                    <pre className="whitespace-pre-wrap text-[11px] leading-relaxed text-white/90 overflow-x-auto">
                      {search.serviceResponse}
                    </pre>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoryItemDisplay; 