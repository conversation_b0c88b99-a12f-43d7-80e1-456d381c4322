import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { ChevronDown, ChevronUp, FileText, Users, Trash2 } from 'lucide-react';

// Assuming ScreenshotAnalysisRecord is defined in a shared types file or passed correctly
// For now, let's redefine it here or ensure it's imported if globally available
interface ScreenshotAnalysisRecord {
  id: string;
  timestamp: number;
  imagePath: string;
  description: string;
  entities: Array<{ type: string; value: string }>;
  rawResponse: string;
  text?: string;
  personSearches?: Array<{
    personName: string;
    serviceResponse: string;
    timestamp: number;
  }>;
}

interface HistoryItemDisplayProps {
  record: ScreenshotAnalysisRecord;
  onDelete?: (recordId: string) => void;
}

const HistoryItemDisplay: React.FC<HistoryItemDisplayProps> = ({ record, onDelete }) => {
  const formattedTimestamp = new Date(record.timestamp).toLocaleString();
  const mermaidChartId = `mermaid-chart-${record.id}`;
  const mermaidContainerRef = useRef<HTMLDivElement>(null);

  // State for expandable sections
  const [showRawResponse, setShowRawResponse] = useState(false);
  const [showPersonSearches, setShowPersonSearches] = useState(false);

  useEffect(() => {
    if (record.entities && record.entities.length > 0 && mermaidContainerRef.current) {
      mermaid.initialize({
        startOnLoad: false,
        theme: 'dark',
        themeVariables: {
          primaryColor: '#3b82f6',
          primaryTextColor: '#ffffff',
          primaryBorderColor: '#1e40af',
          lineColor: '#6b7280',
          secondaryColor: '#1f2937',
          tertiaryColor: '#374151',
          background: '#111827',
          mainBkg: '#1f2937',
          secondBkg: '#374151',
          tertiaryBkg: '#4b5563'
        }
      });

      // Create a more sophisticated knowledge graph
      let graphDefinition = 'graph TB;\n';

      // Group entities by type for better visualization
      const entityGroups: { [key: string]: Array<{ type: string; value: string; index: number }> } = {};
      record.entities.forEach((entity, index) => {
        if (!entityGroups[entity.type]) {
          entityGroups[entity.type] = [];
        }
        entityGroups[entity.type].push({ ...entity, index });
      });

      // Create central analysis node
      const analysisNodeId = `analysis_${record.id.substring(0,6).replace(/-/g, '')}`;
      graphDefinition += `    ${analysisNodeId}["📊 Analysis<br/>${new Date(record.timestamp).toLocaleDateString()}"];\n`;

      // Style the analysis node
      graphDefinition += `    classDef analysisNode fill:#3b82f6,stroke:#1e40af,stroke-width:3px,color:#ffffff;\n`;
      graphDefinition += `    class ${analysisNodeId} analysisNode;\n`;

      // Create entity type nodes and connect them
      const typeColors = {
        'Person': '#10b981',
        'Organization': '#f59e0b',
        'Location': '#ef4444',
        'Date': '#8b5cf6',
        'Technology': '#06b6d4',
        'Product': '#f97316',
        'Event': '#ec4899',
        'Concept': '#84cc16'
      };

      Object.entries(entityGroups).forEach(([entityType, entities]) => {
        const typeNodeId = `type_${entityType.toLowerCase().replace(/\s+/g, '_')}_${record.id.substring(0,6).replace(/-/g, '')}`;
        const color = typeColors[entityType as keyof typeof typeColors] || '#6b7280';

        // Create type node
        graphDefinition += `    ${typeNodeId}["🏷️ ${entityType}<br/>(${entities.length})"];\n`;

        // Connect analysis to type
        graphDefinition += `    ${analysisNodeId} --> ${typeNodeId};\n`;

        // Create individual entity nodes
        entities.forEach((entity) => {
          const entityNodeId = `entity${entity.index}_${record.id.substring(0,6).replace(/-/g, '')}`;
          const entityValue = entity.value.replace(/"/g, '#quot;').substring(0, 30) + (entity.value.length > 30 ? '...' : '');

          graphDefinition += `    ${entityNodeId}["${entityValue}"];\n`;
          graphDefinition += `    ${typeNodeId} --> ${entityNodeId};\n`;
        });

        // Style type nodes
        graphDefinition += `    classDef ${entityType.toLowerCase()}Type fill:${color},stroke:${color},stroke-width:2px,color:#ffffff;\n`;
        graphDefinition += `    class ${typeNodeId} ${entityType.toLowerCase()}Type;\n`;
      });

      // Add relationships between entities of the same type if there are multiple
      Object.entries(entityGroups).forEach(([entityType, entities]) => {
        if (entities.length > 1) {
          for (let i = 0; i < entities.length - 1; i++) {
            const entity1Id = `entity${entities[i].index}_${record.id.substring(0,6).replace(/-/g, '')}`;
            const entity2Id = `entity${entities[i + 1].index}_${record.id.substring(0,6).replace(/-/g, '')}`;
            graphDefinition += `    ${entity1Id} -.-> ${entity2Id};\n`;
          }
        }
      });

      // Style entity nodes
      graphDefinition += `    classDef entityNode fill:#374151,stroke:#6b7280,stroke-width:1px,color:#ffffff;\n`;
      record.entities.forEach((_, index) => {
        const entityNodeId = `entity${index}_${record.id.substring(0,6).replace(/-/g, '')}`;
        graphDefinition += `    class ${entityNodeId} entityNode;\n`;
      });

      // Clear previous render if any, to prevent duplication on re-renders
      mermaidContainerRef.current.innerHTML = '';

      try {
        mermaid.render(mermaidChartId, graphDefinition, (svgCode: string) => {
          if (mermaidContainerRef.current) {
            mermaidContainerRef.current.innerHTML = svgCode;
          }
        });
      } catch (e) {
        console.error("Mermaid rendering error:", e);
        if (mermaidContainerRef.current) {
          mermaidContainerRef.current.innerHTML = "<p class='text-red-400'>Error rendering knowledge graph.</p>";
        }
      }
    } else if (mermaidContainerRef.current) {
        // Clear if no entities or handle accordingly
        mermaidContainerRef.current.innerHTML = '';
    }
  }, [record.entities, record.id, mermaidChartId]);

  return (
    <div className="bg-white/25 backdrop-blur-lg rounded-lg border border-white/20 shadow-lg p-4 text-sm text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] space-y-3">
      {/* Title Section - Using timestamp */}
      <div className="flex justify-between items-center border-b border-white/20 pb-2 mb-2">
        <h3 className="text-[13px] font-medium tracking-wide">
          Analyzed: {formattedTimestamp} (ID: {record.id.substring(0,6)})
        </h3>
        {onDelete && (
          <button
            onClick={() => onDelete(record.id)}
            className="text-red-400 hover:text-red-300 transition-colors p-1 rounded hover:bg-red-500/10"
            title="Delete this record"
          >
            <Trash2 size={14} />
          </button>
        )}
      </div>
      
      {/* Content Area */}
      <div className="space-y-3 text-xs">


        {/* Description */}
        <div>
          <p className="font-semibold mb-0.5">Description:</p>
          <p className="whitespace-pre-wrap">{record.description || "N/A"}</p>
          {record.entities && record.entities.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {Array.from(new Set(record.entities.map(e => e.type))).map(type => {
                const count = record.entities.filter(e => e.type === type).length;
                return (
                  <span
                    key={type}
                    className="inline-flex items-center px-2 py-1 rounded-full text-[10px] bg-blue-500/20 text-blue-300 border border-blue-500/30"
                  >
                    {type}: {count}
                  </span>
                );
              })}
            </div>
          )}
        </div>

        {/* Knowledge Graph */}
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Users size={14} />
            <p className="font-semibold">Knowledge Graph:</p>
          </div>
          {record.entities && record.entities.length > 0 ? (
            <div className="bg-gray-900/50 rounded-lg p-3 border border-white/10">
              <div ref={mermaidContainerRef} id={mermaidChartId} className="mermaid-graph-container w-full overflow-x-auto">
                {/* Knowledge graph will be rendered here */}
              </div>
              <p className="text-[10px] text-white/60 mt-2 italic">
                Interactive knowledge graph showing relationships between identified entities
              </p>
            </div>
          ) : (
            <p className="italic">No entities identified to display in knowledge graph.</p>
          )}
        </div>

        {/* Raw Response - Expandable */}
        {record.rawResponse && (
          <div>
            <button
              onClick={() => setShowRawResponse(!showRawResponse)}
              className="flex items-center gap-2 mb-1 hover:text-white/80 transition-colors"
            >
              <FileText size={14} />
              <p className="font-semibold">Raw Response:</p>
              {showRawResponse ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
            </button>
            {showRawResponse && (
              <div className="bg-black/20 rounded p-2 border border-white/10">
                <pre className="whitespace-pre-wrap text-[11px] leading-relaxed text-white/90 overflow-x-auto">
                  {record.rawResponse}
                </pre>
              </div>
            )}
          </div>
        )}

        {/* Person Searches - Expandable */}
        {record.personSearches && record.personSearches.length > 0 && (
          <div>
            <button
              onClick={() => setShowPersonSearches(!showPersonSearches)}
              className="flex items-center gap-2 mb-1 hover:text-white/80 transition-colors"
            >
              <Users size={14} />
              <p className="font-semibold">Person Searches ({record.personSearches.length}):</p>
              {showPersonSearches ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
            </button>
            {showPersonSearches && (
              <div className="space-y-2">
                {record.personSearches.map((search, index) => (
                  <div key={index} className="bg-black/20 rounded p-2 border border-white/10">
                    <div className="flex justify-between items-center mb-1">
                      <p className="font-medium text-blue-300">{search.personName}</p>
                      <p className="text-[10px] text-white/60">
                        {new Date(search.timestamp).toLocaleString()}
                      </p>
                    </div>
                    <pre className="whitespace-pre-wrap text-[11px] leading-relaxed text-white/90 overflow-x-auto">
                      {search.serviceResponse}
                    </pre>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default HistoryItemDisplay; 