import React, { useEffect, useRef } from 'react';
import mermaid from 'mermaid';

// Assuming ScreenshotAnalysisRecord is defined in a shared types file or passed correctly
// For now, let's redefine it here or ensure it's imported if globally available
interface ScreenshotAnalysisRecord {
  id: string;
  timestamp: number;
  imagePath: string;
  description: string;
  entities: Array<{ type: string; value: string }>;
  rawResponse: string;
  text?: string;
}

interface HistoryItemDisplayProps {
  record: ScreenshotAnalysisRecord;
}

const HistoryItemDisplay: React.FC<HistoryItemDisplayProps> = ({ record }) => {
  const formattedTimestamp = new Date(record.timestamp).toLocaleString();
  const mermaidChartId = `mermaid-chart-${record.id}`;
  const mermaidContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (record.entities && record.entities.length > 0 && mermaidContainerRef.current) {
      mermaid.initialize({ startOnLoad: false, theme: 'neutral' }); // Using neutral theme for better compatibility

      let graphDefinition = 'graph TD;\n';
      record.entities.forEach((entity, index) => {
        // Ensure node IDs are simple and valid for Mermaid
        const nodeId = `entity${index}_${record.id.substring(0,6).replace(/-/g, '')}`; 
        // Escape special characters in entity type and value for the label
        const entityType = entity.type.replace(/"/g, '#quot;');
        const entityValue = entity.value.replace(/"/g, '#quot;');
        graphDefinition += `    ${nodeId}["<b>${entityType}</b><br/>${entityValue}"];\n`;
      });
      
      // Clear previous render if any, to prevent duplication on re-renders
      mermaidContainerRef.current.innerHTML = '';
      
      try {
        mermaid.render(mermaidChartId, graphDefinition, (svgCode: string) => {
          if (mermaidContainerRef.current) {
            mermaidContainerRef.current.innerHTML = svgCode;
          }
        });
      } catch (e) {
        console.error("Mermaid rendering error:", e);
        if (mermaidContainerRef.current) {
          mermaidContainerRef.current.innerHTML = "<p class=\'text-red-400\'>Error rendering graph.</p>";
        }
      }
    } else if (mermaidContainerRef.current) {
        // Clear if no entities or handle accordingly
        mermaidContainerRef.current.innerHTML = ''; 
    }
  }, [record.entities, record.id, mermaidChartId]);

  return (
    <div className="bg-white/25 backdrop-blur-lg rounded-lg border border-white/20 shadow-lg p-4 text-sm text-white [text-shadow:0_1px_3px_rgba(0,0,0,0.4)] space-y-3">
      {/* Title Section - Using timestamp */}
      <h3 className="text-[13px] font-medium tracking-wide border-b border-white/20 pb-2 mb-2">
        Analyzed: {formattedTimestamp} (ID: {record.id.substring(0,6)})
      </h3>
      
      {/* Content Area */}
      <div className="space-y-2 text-xs">
        <div>
          <p className="font-semibold mb-0.5">Description:</p>
          <p className="whitespace-pre-wrap">{record.description || "N/A"}</p>
        </div>

        <div>
          <p className="font-semibold mb-1">Entities Graph:</p>
          {record.entities && record.entities.length > 0 ? (
            <div ref={mermaidContainerRef} id={mermaidChartId} className="mermaid-graph-container w-full overflow-x-auto">
              {/* Mermaid graph will be rendered here */}
            </div>
          ) : (
            <p className="italic">No specific entities identified to display in a graph.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default HistoryItemDisplay; 