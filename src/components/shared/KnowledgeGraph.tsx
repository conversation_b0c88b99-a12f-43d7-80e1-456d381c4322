import React, { useEffect, useRef } from 'react';
import mermaid from 'mermaid';
import { Users } from 'lucide-react';

interface Entity {
  type: string;
  value: string;
}

interface KnowledgeGraphProps {
  entities: Entity[];
  recordId: string;
  timestamp?: number;
  title?: string;
  showStats?: boolean;
  className?: string;
}

const KnowledgeGraph: React.FC<KnowledgeGraphProps> = ({ 
  entities, 
  recordId, 
  timestamp, 
  title = "Knowledge Graph",
  showStats = true,
  className = ""
}) => {
  const mermaidChartId = `mermaid-chart-${recordId}`;
  const mermaidContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (entities && entities.length > 0 && mermaidContainerRef.current) {
      mermaid.initialize({ 
        startOnLoad: false, 
        theme: 'dark',
        themeVariables: {
          primaryColor: '#3b82f6',
          primaryTextColor: '#ffffff',
          primaryBorderColor: '#1e40af',
          lineColor: '#6b7280',
          secondaryColor: '#1f2937',
          tertiaryColor: '#374151',
          background: '#111827',
          mainBkg: '#1f2937',
          secondBkg: '#374151',
          tertiaryBkg: '#4b5563'
        }
      });

      // Create a sophisticated knowledge graph
      let graphDefinition = 'graph TB;\n';
      
      // Group entities by type for better visualization
      const entityGroups: { [key: string]: Array<{ type: string; value: string; index: number }> } = {};
      entities.forEach((entity, index) => {
        if (!entityGroups[entity.type]) {
          entityGroups[entity.type] = [];
        }
        entityGroups[entity.type].push({ ...entity, index });
      });

      // Create central analysis node
      const analysisNodeId = `analysis_${recordId.substring(0,6).replace(/-/g, '')}`;
      const analysisLabel = timestamp 
        ? `📊 Analysis<br/>${new Date(timestamp).toLocaleDateString()}`
        : '📊 Current Analysis';
      graphDefinition += `    ${analysisNodeId}["${analysisLabel}"];\n`;
      
      // Style the analysis node
      graphDefinition += `    classDef analysisNode fill:#3b82f6,stroke:#1e40af,stroke-width:3px,color:#ffffff;\n`;
      graphDefinition += `    class ${analysisNodeId} analysisNode;\n`;

      // Create entity type nodes and connect them
      const typeColors = {
        'Person': '#10b981',
        'Organization': '#f59e0b', 
        'Location': '#ef4444',
        'Date': '#8b5cf6',
        'Technology': '#06b6d4',
        'Product': '#f97316',
        'Event': '#ec4899',
        'Concept': '#84cc16'
      };

      Object.entries(entityGroups).forEach(([entityType, entities]) => {
        const typeNodeId = `type_${entityType.toLowerCase().replace(/\s+/g, '_')}_${recordId.substring(0,6).replace(/-/g, '')}`;
        const color = typeColors[entityType as keyof typeof typeColors] || '#6b7280';
        
        // Create type node
        graphDefinition += `    ${typeNodeId}["🏷️ ${entityType}<br/>(${entities.length})"];\n`;
        
        // Connect analysis to type
        graphDefinition += `    ${analysisNodeId} --> ${typeNodeId};\n`;
        
        // Create individual entity nodes
        entities.forEach((entity) => {
          const entityNodeId = `entity${entity.index}_${recordId.substring(0,6).replace(/-/g, '')}`;
          const entityValue = entity.value.replace(/"/g, '#quot;').substring(0, 30) + (entity.value.length > 30 ? '...' : '');
          
          graphDefinition += `    ${entityNodeId}["${entityValue}"];\n`;
          graphDefinition += `    ${typeNodeId} --> ${entityNodeId};\n`;
        });
        
        // Style type nodes
        graphDefinition += `    classDef ${entityType.toLowerCase()}Type fill:${color},stroke:${color},stroke-width:2px,color:#ffffff;\n`;
        graphDefinition += `    class ${typeNodeId} ${entityType.toLowerCase()}Type;\n`;
      });

      // Add relationships between entities of the same type if there are multiple
      Object.entries(entityGroups).forEach(([entityType, entities]) => {
        if (entities.length > 1) {
          for (let i = 0; i < entities.length - 1; i++) {
            const entity1Id = `entity${entities[i].index}_${recordId.substring(0,6).replace(/-/g, '')}`;
            const entity2Id = `entity${entities[i + 1].index}_${recordId.substring(0,6).replace(/-/g, '')}`;
            graphDefinition += `    ${entity1Id} -.-> ${entity2Id};\n`;
          }
        }
      });

      // Style entity nodes
      graphDefinition += `    classDef entityNode fill:#374151,stroke:#6b7280,stroke-width:1px,color:#ffffff;\n`;
      entities.forEach((_, index) => {
        const entityNodeId = `entity${index}_${recordId.substring(0,6).replace(/-/g, '')}`;
        graphDefinition += `    class ${entityNodeId} entityNode;\n`;
      });
      
      // Clear previous render if any, to prevent duplication on re-renders
      mermaidContainerRef.current.innerHTML = '';
      
      try {
        mermaid.render(mermaidChartId, graphDefinition, (svgCode: string) => {
          if (mermaidContainerRef.current) {
            mermaidContainerRef.current.innerHTML = svgCode;
          }
        });
      } catch (e) {
        console.error("Mermaid rendering error:", e);
        if (mermaidContainerRef.current) {
          mermaidContainerRef.current.innerHTML = "<p class='text-red-400'>Error rendering knowledge graph.</p>";
        }
      }
    } else if (mermaidContainerRef.current) {
        // Clear if no entities or handle accordingly
        mermaidContainerRef.current.innerHTML = ''; 
    }
  }, [entities, recordId, mermaidChartId, timestamp]);

  if (!entities || entities.length === 0) {
    return (
      <div className={className}>
        <div className="flex items-center gap-2 mb-1">
          <Users size={14} />
          <p className="font-semibold text-[13px]">{title}:</p>
        </div>
        <p className="italic text-xs">No entities identified to display in knowledge graph.</p>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center gap-2 mb-2">
        <Users size={14} />
        <p className="font-semibold text-[13px]">{title}:</p>
      </div>
      
      {/* Entity Statistics */}
      {showStats && (
        <div className="mb-3 flex flex-wrap gap-1">
          {Array.from(new Set(entities.map(e => e.type))).map(type => {
            const count = entities.filter(e => e.type === type).length;
            return (
              <span 
                key={type}
                className="inline-flex items-center px-2 py-1 rounded-full text-[10px] bg-blue-500/20 text-blue-300 border border-blue-500/30"
              >
                {type}: {count}
              </span>
            );
          })}
        </div>
      )}

      {/* Knowledge Graph */}
      <div className="bg-gray-900/50 rounded-lg p-3 border border-white/10">
        <div ref={mermaidContainerRef} id={mermaidChartId} className="mermaid-graph-container w-full overflow-x-auto">
          {/* Knowledge graph will be rendered here */}
        </div>
        <p className="text-[10px] text-white/60 mt-2 italic">
          Interactive knowledge graph showing relationships between identified entities
        </p>
      </div>
    </div>
  );
};

export default KnowledgeGraph;
