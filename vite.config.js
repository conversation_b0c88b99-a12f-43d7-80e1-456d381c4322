import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import electron from 'vite-plugin-electron';
import renderer from 'vite-plugin-electron-renderer';
import path from 'node:path';

export default defineConfig(({ command, mode }) => {
  const isServe = command === 'serve';
  const isBuild = command === 'build';
  const sourcemap = isServe || !!process.env.VSCODE_DEBUG;

  // Load .env files based on mode
  // const env = loadEnv(mode, process.cwd(), ''); // if you need env vars in vite config itself

  return {
    // Critical: Set base to './' for production builds to ensure correct asset paths for file:// URLs
    base: isBuild ? './' : '/',
    plugins: [
      react(),
      electron([
        {
          // Main-Process entry file of the Electron App.
          entry: 'electron/main.ts',
          vite: {
            build: {
              sourcemap,
              minify: isBuild ? 'terser' : false,
              terserOptions: isBuild ? {
                compress: {
                  drop_console: true,
                },
              } : undefined,
              outDir: 'dist-electron',
              rollupOptions: {
                // Consider listing external dependencies if any are not meant to be bundled
                external: ['electron', 'exa-js'],
              },
            },
          },
        },
        {
          // Preload-Scripts entry file.
          entry: 'electron/preload.ts',
          onstart(options) {
            // Notify the Renderer-Process to reload the page when the Preload-Scripts build is complete,
            // instead of restarting the entire Electron App.
            options.reload();
          },
          vite: {
            build: {
              sourcemap: sourcemap ? 'inline' : undefined,
              minify: isBuild ? 'terser' : false,
              terserOptions: isBuild ? {
                compress: {
                  drop_console: true,
                },
              } : undefined,
              outDir: 'dist-electron',
              rollupOptions: {
                // external: ['electron'],
              },
            },
          },
        }
      ]),
      renderer({
        // Options for the Electron Renderer process
        // nodeIntegration: true, // Be cautious with this, default is false
        // html入口文件， plent Default: `index.html`
        // outputDir: 'dist', (Vite default)
      }),
    ],
    // If your index.html is not in the root, specify the root.
    // root: process.cwd(), // Default is project root where vite.config.js is.
    build: {
      // This is for the renderer build
      outDir: 'dist', // Vite's default output directory for the renderer
      // assetsDir: 'assets', // Vite's default
      sourcemap: sourcemap ? 'inline' : false,
      minify: isBuild ? 'terser' : false,
      terserOptions: isBuild ? {
        compress: {
          drop_console: true,
        },
      } : undefined,
    },
    resolve: {
      alias: {
        // Example: '@': path.resolve(__dirname, 'src'),
      },
    },
  };
});
