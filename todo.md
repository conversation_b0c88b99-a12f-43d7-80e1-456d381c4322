- [ ] Allow further people search through an API
- [ ] Make the results from screenshot more presentable
- [ ] Build a welcome screen for the app
- [ ] Implement user management with web app login directly in the app
- [ ] Add chat functionality with a global shortcut (Shift + Enter)
- [ ] UI: Enhance visual feedback during active audio recording
- [ ] UI: Improve presentation of screenshots in the queue (e.g., larger previews, naming)
- [ ] UI: Refine the layout of the "Solutions" view for better readability
- [ ] UI: Add a more prominent global "Processing" indicator during AI analysis
- [ ] Memory: Add long time memory to able to search through people you met before 

https://x.com/firecrawl_dev/status/1932861885180940540




Add notes for memory 

https://github.com/TriliumNext/Notes

